<template>
  <Dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1200px"
    max-height="700px"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="resource-binding-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="运维用户" prop="opsUserId">
            <el-select
              v-model="formData.opsUserId"
              placeholder="请选择运维用户"
              clearable
              class="w-full"
              @change="handleUserChange"
            >
              <el-option 
                v-for="user in userList" 
                :key="user.id" 
                :label="user.nickname" 
                :value="user.id" 
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="运维部门" prop="opsDeptId">
            <el-select
              v-model="formData.opsDeptId"
              placeholder="请选择运维部门"
              clearable
              class="w-full"
            >
              <el-option 
                v-for="dept in deptList" 
                :key="dept.id" 
                :label="dept.deptName" 
                :value="dept.id" 
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="绑定配置">
        <el-tabs v-model="activeTab" type="card" class="w-full">
          <!-- 产品渠道批量配置 -->
          <el-tab-pane label="产品渠道权限" name="productChannel">
            <div class="batch-config-panel">
              <div class="config-header">
                <el-alert 
                  title="选择产品和渠道，点击添加按钮加入权限列表" 
                  type="info" 
                  :closable="false" 
                  show-icon 
                />
              </div>
              
              <el-row :gutter="20" class="mt-4">
                <el-col :span="10">
                  <el-form-item label="选择产品">
                    <el-select
                      v-model="currentProductId"
                      placeholder="请选择产品"
                      clearable
                      class="w-full"
                    >
                      <el-option 
                        v-for="product in productList" 
                        :key="product.id" 
                        :label="`${product.name} (ID: ${product.id})`" 
                        :value="product.id" 
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="10">
                  <el-form-item label="选择渠道">
                    <el-select
                      v-model="currentChannelCode"
                      placeholder="请选择渠道"
                      clearable
                      class="w-full"
                    >
                      <el-option 
                        v-for="channel in channelList" 
                        :key="channel.code" 
                        :label="`${channel.name} (${channel.code})`" 
                        :value="channel.code" 
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label=" ">
                    <el-button 
                      type="primary" 
                      @click="addProductChannelCombination"
                      :disabled="!currentProductId || !currentChannelCode"
                      class="w-full"
                    >
                      <Icon icon="ep:plus" class="mr-1" />
                      添加
                    </el-button>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <!-- 已添加的组合列表 -->
              <div v-if="formData.productChannelCombinations.length > 0" class="combinations-list">
                <div class="flex items-center justify-between mb-3">
                  <h4 class="text-sm font-medium">已添加的权限组合</h4>
                  <div class="flex items-center space-x-2">
                    <el-tag type="success">共 {{ formData.productChannelCombinations.length }} 个组合</el-tag>
                    <el-button 
                      type="danger" 
                      text 
                      size="small"
                      @click="clearAllProductChannelCombinations"
                    >
                      清空所有
                    </el-button>
                  </div>
                </div>
                <div class="combinations-grid">
                  <div
                    v-for="(combo, index) in formData.productChannelCombinations"
                    :key="`${combo.productId}-${combo.channelCode}`"
                    class="combination-item"
                  >
                    <div class="combination-content">
                      <div class="product-info">
                        <el-tag size="small" type="primary">
                          {{ getProductName(combo.productId) }}
                        </el-tag>
                      </div>
                      <div class="channel-info">
                        <el-tag size="small" type="warning">
                          {{ getChannelName(combo.channelCode) }}
                        </el-tag>
                      </div>
                    </div>
                    <el-button 
                      type="danger" 
                      text 
                      size="small"
                      @click="removeProductChannelCombination(index)"
                    >
                      <Icon icon="ep:delete" />
                    </el-button>
                  </div>
                </div>
              </div>
              
              <!-- 空状态提示 -->
              <div v-else class="empty-state">
                <el-empty 
                  description="暂无权限组合，请选择产品和渠道后点击添加"
                  :image-size="80"
                />
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 区服批量配置 -->
          <el-tab-pane label="区服权限" name="server">
            <div class="batch-config-panel">
              <div class="config-header">
                <el-alert 
                  title="配置区服权限，可选择关联的产品和渠道" 
                  type="info" 
                  :closable="false" 
                  show-icon 
                />
              </div>
              
              <el-row :gutter="20" class="mt-4">
                <el-col :span="8">
                  <el-form-item label="关联产品">
                    <el-select
                      v-model="currentServerProductId"
                      placeholder="请选择产品（可选）"
                      clearable
                      class="w-full"
                    >
                      <el-option 
                        v-for="product in productList" 
                        :key="product.id" 
                        :label="`${product.name} (ID: ${product.id})`" 
                        :value="product.id" 
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="关联渠道">
                    <el-select
                      v-model="currentServerChannelCode"
                      placeholder="请选择渠道（可选）"
                      clearable
                      class="w-full"
                    >
                      <el-option 
                        v-for="channel in channelList" 
                        :key="channel.code" 
                        :label="`${channel.name} (${channel.code})`" 
                        :value="channel.code" 
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="区服名称">
                    <el-input
                      v-model="currentServerName"
                      placeholder="输入区服名称"
                      @keyup.enter="addServerPermission"
                    >
                      <template #append>
                        <el-button 
                          @click="addServerPermission" 
                          type="primary"
                          :disabled="!currentServerName.trim()"
                        >
                          添加
                        </el-button>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <!-- 已添加的区服权限 -->
              <div v-if="formData.serverPermissions.length > 0" class="server-permissions-list">
                <div class="flex items-center justify-between mb-3">
                  <h4 class="text-sm font-medium">已添加的区服权限</h4>
                  <div class="flex items-center space-x-2">
                    <el-tag type="success">共 {{ formData.serverPermissions.length }} 个区服</el-tag>
                    <el-button 
                      type="danger" 
                      text 
                      size="small"
                      @click="clearAllServerPermissions"
                    >
                      清空所有
                    </el-button>
                  </div>
                </div>
                <div class="server-permissions-grid">
                  <div
                    v-for="(server, index) in formData.serverPermissions"
                    :key="index"
                    class="server-permission-item"
                  >
                    <div class="server-content">
                      <div class="server-name">
                        <el-tag type="success">{{ server.serverName }}</el-tag>
                      </div>
                      <div class="server-details">
                        <span v-if="server.productId" class="detail-item">
                          产品: {{ getProductName(server.productId) }}
                        </span>
                        <span v-if="server.channelCode" class="detail-item">
                          渠道: {{ getChannelName(server.channelCode) }}
                        </span>
                      </div>
                    </div>
                    <el-button 
                      type="danger" 
                      text 
                      size="small"
                      @click="removeServerPermission(index)"
                    >
                      <Icon icon="ep:delete" />
                    </el-button>
                  </div>
                </div>
              </div>
              
              <!-- 空状态提示 -->
              <div v-else class="empty-state">
                <el-empty 
                  description="暂无区服权限，请输入区服名称后点击添加"
                  :image-size="80"
                />
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 运营小组配置 -->
          <el-tab-pane label="运营小组" name="operationTeam">
            <div class="batch-config-panel">
              <div class="config-header">
                <el-alert 
                  title="选择目标部门并输入运营小组名称，点击添加按钮加入权限列表" 
                  type="info" 
                  :closable="false" 
                  show-icon 
                />
              </div>
              
              <el-row :gutter="20" class="mt-4">
                <el-col :span="10">
                  <el-form-item label="目标部门">
                    <el-select
                      v-model="currentTargetDeptId"
                      placeholder="请选择目标部门"
                      clearable
                      class="w-full"
                    >
                      <el-option 
                        v-for="dept in operationDeptList" 
                        :key="dept.id" 
                        :label="dept.deptName" 
                        :value="dept.id" 
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="10">
                  <el-form-item label="运营小组">
                    <el-input
                      v-model="currentOperationTeamName"
                      placeholder="输入运营小组名称"
                      @keyup.enter="addOperationTeamPermission"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label=" ">
                    <el-button 
                      type="primary" 
                      @click="addOperationTeamPermission"
                      :disabled="!currentOperationTeamName.trim()"
                      class="w-full"
                    >
                      <Icon icon="ep:plus" class="mr-1" />
                      添加
                    </el-button>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <!-- 已添加的运营小组权限 -->
              <div v-if="formData.operationTeamPermissions.length > 0" class="operation-team-permissions-list">
                <div class="flex items-center justify-between mb-3">
                  <h4 class="text-sm font-medium">已添加的运营小组权限</h4>
                  <div class="flex items-center space-x-2">
                    <el-tag type="success">共 {{ formData.operationTeamPermissions.length }} 个小组</el-tag>
                    <el-button 
                      type="danger" 
                      text 
                      size="small"
                      @click="clearAllOperationTeamPermissions"
                    >
                      清空所有
                    </el-button>
                  </div>
                </div>
                <div class="operation-team-permissions-grid">
                  <div
                    v-for="(team, index) in formData.operationTeamPermissions"
                    :key="index"
                    class="operation-team-permission-item"
                  >
                    <div class="team-content">
                      <div class="team-name">
                        <el-tag type="warning">{{ team.teamName }}</el-tag>
                      </div>
                      <div class="team-details" v-if="team.targetDeptId">
                        <span class="detail-item">
                          部门: {{ getDeptName(team.targetDeptId) }}
                        </span>
                      </div>
                    </div>
                    <el-button 
                      type="danger" 
                      text 
                      size="small"
                      @click="removeOperationTeamPermission(index)"
                    >
                      <Icon icon="ep:delete" />
                    </el-button>
                  </div>
                </div>
              </div>
              
              <!-- 空状态提示 -->
              <div v-else class="empty-state">
                <el-empty 
                  description="暂无运营小组权限，请输入小组名称后点击添加"
                  :image-size="80"
                />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-form-item>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态">
            <el-radio-group v-model="formData.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">停用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="操作模式">
            <el-select v-model="formData.operationMode" class="w-full">
              <el-option label="替换所有绑定" value="REPLACE" />
              <el-option label="增量添加绑定" value="ADD" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="备注">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="submitForm" :loading="loading">
        确定
      </el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { reactive, ref, nextTick, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { UserResourceBindingApi } from '@/api/erp/resourceBinding'
import { DepartmentApi } from '@/api/erp/department'
import * as UserApi from '@/api/system/user'

const emit = defineEmits(['success'])

const dialogVisible = ref(false)
const dialogTitle = ref('')
const loading = ref(false)
const formRef = ref()
const activeTab = ref('productChannel')

// 输入框状态
const serverNameInput = ref('')
const operationTeamInput = ref('')

// 当前选择的值
const currentProductId = ref<number | undefined>(undefined)
const currentChannelCode = ref<string | undefined>(undefined)
const currentServerProductId = ref<number | undefined>(undefined)
const currentServerChannelCode = ref<string | undefined>(undefined)
const currentServerName = ref('')
const currentTargetDeptId = ref<number | undefined>(undefined)
const currentOperationTeamName = ref('')

// 数据列表
const userList = ref<any[]>([])
const deptList = ref<any[]>([])
const operationDeptList = ref<any[]>([])
const productList = ref<any[]>([])
const channelList = ref<any[]>([])

// 表单数据
const formData = reactive({
  opsUserId: undefined,
  opsDeptId: undefined,
  productChannelCombinations: [] as Array<{ productId: number; channelCode: string }>,
  serverPermissions: [] as Array<{ serverName: string; productId?: number; channelCode?: string }>,
  operationTeams: [] as string[],
  operationTeamPermissions: [] as Array<{ teamName: string; targetDeptId?: number }>,
  targetDeptId: undefined,
  status: 1,
  operationMode: 'REPLACE',
  remark: ''
})

// 表单规则
const formRules = reactive({
  opsUserId: [
    { required: true, message: '请选择运维用户', trigger: 'change' }
  ],
  opsDeptId: [
    { required: true, message: '请选择运维部门', trigger: 'change' }
  ]
})

/** 打开弹窗 */
const open = async (type = 'create', row?: any) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '配置资源绑定' : '编辑资源绑定'
  resetForm()
  
  // 获取数据
  await Promise.all([
    getUserList(),
    getDeptList(),
    getOperationDeptList(),
    getProductList(),
    getChannelList()
  ])
  
  if (type === 'edit' && row) {
    Object.assign(formData, row)
  }
}

/** 关闭弹窗 */
const cancel = () => {
  dialogVisible.value = false
  resetForm()
}

/** 重置表单 */
const resetForm = () => {
  formData.opsUserId = undefined
  formData.opsDeptId = undefined
  formData.productChannelCombinations = []
  formData.serverPermissions = []
  formData.operationTeams = []
  formData.operationTeamPermissions = []
  formData.targetDeptId = undefined
  formData.status = 1
  formData.operationMode = 'REPLACE'
  formData.remark = ''
  
  // 重置输入框
  serverNameInput.value = ''
  operationTeamInput.value = ''
  
  // 重置当前选择的值
  currentProductId.value = undefined
  currentChannelCode.value = undefined
  currentServerProductId.value = undefined
  currentServerChannelCode.value = undefined
  currentServerName.value = ''
  currentTargetDeptId.value = undefined
  currentOperationTeamName.value = ''
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

/** 提交表单 */
const submitForm = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate()
  if (!valid) return
  
  // 严格验证必填字段
  if (!formData.opsUserId || !formData.opsDeptId) {
    ElMessage.error('请选择运维用户和部门')
    return
  }
  
  // 构建绑定数据
  const bindings: any[] = []
  
  // 产品渠道绑定
  if (formData.productChannelCombinations.length > 0) {
    bindings.push({
      opsUserId: formData.opsUserId!,
      opsDeptId: formData.opsDeptId!,
      productChannelCombinations: formData.productChannelCombinations,
      status: formData.status,
      remark: formData.remark || ''
    })
  }
  
  // 区服绑定
  if (formData.serverPermissions.length > 0) {
    bindings.push({
      opsUserId: formData.opsUserId!,
      opsDeptId: formData.opsDeptId!,
      serverPermissions: formData.serverPermissions,
      status: formData.status,
      remark: formData.remark || ''
    })
  }
  
  // 运营小组绑定
  if (formData.operationTeamPermissions.length > 0) {
    bindings.push({
      opsUserId: formData.opsUserId!,
      opsDeptId: formData.opsDeptId!,
      operationTeamPermissions: formData.operationTeamPermissions,
      status: formData.status,
      remark: formData.remark || ''
    })
  }
  
  if (bindings.length === 0) {
    ElMessage.error('请至少配置一种资源绑定')
    return
  }
  
  loading.value = true
  try {
    await UserResourceBindingApi.batchSetUserResourceBinding({
      userId: formData.opsUserId!,
      operationMode: formData.operationMode as any,
      bindings: bindings
    } as any)
    
    ElMessage.success('保存成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    loading.value = false
  }
}

/** 用户变化处理 */
const handleUserChange = (userId: number) => {
  // 可以根据用户自动获取所属部门
  // 这里暂时手动选择
}

/** 添加产品渠道组合 */
const addProductChannelCombination = () => {
  if (currentProductId.value && currentChannelCode.value) {
    const newCombo = {
      productId: currentProductId.value,
      channelCode: currentChannelCode.value
    }
    if (!formData.productChannelCombinations.some(combo => 
      combo.productId === newCombo.productId && combo.channelCode === newCombo.channelCode
    )) {
      formData.productChannelCombinations.push(newCombo)
      currentProductId.value = undefined
      currentChannelCode.value = undefined
    } else {
      ElMessage.warning('该组合已存在')
    }
  } else {
    ElMessage.warning('请选择产品和渠道')
  }
}

/** 删除产品渠道组合 */
const removeProductChannelCombination = (index: number) => {
  formData.productChannelCombinations.splice(index, 1)
}

/** 清空所有产品渠道组合 */
const clearAllProductChannelCombinations = () => {
  formData.productChannelCombinations = []
}

/** 添加区服权限 */
const addServerPermission = () => {
  const serverName = currentServerName.value.trim()
  if (serverName) {
    const newServer = {
      serverName: serverName,
      productId: currentServerProductId.value,
      channelCode: currentServerChannelCode.value
    }
    if (!formData.serverPermissions.some(server => 
      server.serverName === newServer.serverName && 
      server.productId === newServer.productId && 
      server.channelCode === newServer.channelCode
    )) {
      formData.serverPermissions.push(newServer)
      currentServerName.value = ''
      currentServerProductId.value = undefined
      currentServerChannelCode.value = undefined
    } else {
      ElMessage.warning('该区服权限已存在')
    }
  } else {
    ElMessage.warning('区服名称不能为空')
  }
}

/** 删除区服权限 */
const removeServerPermission = (index: number) => {
  formData.serverPermissions.splice(index, 1)
}

/** 清空所有区服权限 */
const clearAllServerPermissions = () => {
  formData.serverPermissions = []
}

/** 获取产品名称 */
const getProductName = (productId: number) => {
  const product = productList.value.find(p => p.id === productId)
  return product ? product.name : `产品ID: ${productId}`
}

/** 获取渠道名称 */
const getChannelName = (channelCode: string) => {
  const channel = channelList.value.find(c => c.code === channelCode)
  return channel ? channel.name : `渠道代码: ${channelCode}`
}

/** 添加运营小组 */
const addOperationTeam = () => {
  const team = operationTeamInput.value.trim()
  if (team && !formData.operationTeams.includes(team)) {
    formData.operationTeams.push(team)
    operationTeamInput.value = ''
  } else if (team) {
    ElMessage.warning('该运营小组已存在')
  } else {
    ElMessage.warning('运营小组名称不能为空')
  }
}

/** 删除运营小组 */
const removeOperationTeam = (team: string) => {
  const index = formData.operationTeams.indexOf(team)
  if (index > -1) {
    formData.operationTeams.splice(index, 1)
  }
}

/** 添加运营小组权限 */
const addOperationTeamPermission = () => {
  const teamName = currentOperationTeamName.value.trim()
  if (teamName) {
    const newTeam = {
      teamName: teamName,
      targetDeptId: currentTargetDeptId.value
    }
    if (!formData.operationTeamPermissions.some(team => 
      team.teamName === newTeam.teamName && team.targetDeptId === newTeam.targetDeptId
    )) {
      formData.operationTeamPermissions.push(newTeam)
      currentOperationTeamName.value = ''
      currentTargetDeptId.value = undefined
    } else {
      ElMessage.warning('该运营小组权限已存在')
    }
  } else {
    ElMessage.warning('运营小组名称不能为空')
  }
}

/** 删除运营小组权限 */
const removeOperationTeamPermission = (index: number) => {
  formData.operationTeamPermissions.splice(index, 1)
}

/** 清空所有运营小组权限 */
const clearAllOperationTeamPermissions = () => {
  formData.operationTeamPermissions = []
}

/** 获取部门名称 */
const getDeptName = (deptId: number) => {
  const dept = operationDeptList.value.find(d => d.id === deptId)
  return dept ? dept.deptName : `部门ID: ${deptId}`
}

/** 获取用户列表 */
const getUserList = async () => {
  try {
    const data = await UserApi.getUserPage({
      pageNo: 1,
      pageSize: 100
    })
    userList.value = data.list || []
  } catch (error) {
    console.error('获取用户列表失败:', error)
  }
}

/** 获取运维部门列表 */
const getDeptList = async () => {
  try {
    const data = await DepartmentApi.getDepartmentTree()
    deptList.value = data || []
  } catch (error) {
    console.error('获取部门列表失败:', error)
  }
}

/** 获取运营部门列表 */
const getOperationDeptList = async () => {
  try {
    console.log('开始加载运营部门列表...')
    const data = await DepartmentApi.getDepartmentTree()
    console.log('getDepartmentTree API返回数据:', data)

    // 过滤包含"运营"的部门
    const filteredDepts = data?.filter((dept) => dept.deptName?.includes('运营')) || []
    console.log('过滤后的运营部门列表:', filteredDepts)

    // 如果没有找到包含"运营"的部门，则使用所有部门作为降级处理
    if (filteredDepts.length === 0 && data && data.length > 0) {
      console.warn('未找到包含"运营"字样的部门，使用所有部门作为降级处理')
      operationDeptList.value = data
      ElMessage.warning('未找到运营部门，显示所有部门供选择')
    } else {
      operationDeptList.value = filteredDepts
    }

    console.log('最终的运营部门列表:', operationDeptList.value)
  } catch (error) {
    console.error('获取运营部门列表失败:', error)
  }
}

/** 获取产品列表 */
const getProductList = async () => {
  try {
    // 模拟产品数据，实际应该调用产品API
    productList.value = [
      { id: 1, name: '仙剑奇侠传' },
      { id: 2, name: '轩辕剑' },
      { id: 3, name: '大话西游' },
      { id: 4, name: '梦幻西游' },
      { id: 5, name: '问道' }
    ]
  } catch (error) {
    console.error('获取产品列表失败:', error)
  }
}

/** 获取渠道列表 */
const getChannelList = async () => {
  try {
    // 模拟渠道数据，实际应该调用渠道API
    channelList.value = [
      { code: 'ANDROID', name: '安卓官方' },
      { code: 'IOS', name: 'iOS官方' },
      { code: 'HUAWEI', name: '华为' },
      { code: 'XIAOMI', name: '小米' },
      { code: 'OPPO', name: 'OPPO' },
      { code: 'VIVO', name: 'VIVO' },
      { code: 'BILIBILI', name: 'B站' },
      { code: 'TAPTAP', name: 'TapTap' }
    ]
  } catch (error) {
    console.error('获取渠道列表失败:', error)
  }
}

defineExpose({ open })
</script>

<style scoped>
.resource-binding-form {
  max-height: 600px;
  overflow-y: auto;
}

.batch-config-panel {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.config-header {
  margin-bottom: 16px;
}

.combinations-list {
  background: white;
  border-radius: 6px;
  padding: 12px;
  margin-top: 16px;
  border: 1px solid #e4e7ed;
}

.combinations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 8px;
}

.combination-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f0f9eb;
  border: 1px solid #e1f3d8;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 0.9em;
  color: #67c23a;
}

.combination-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.product-info, .channel-info {
  display: flex;
  align-items: center;
}

.server-permissions-list {
  background: white;
  border-radius: 6px;
  padding: 12px;
  margin-top: 16px;
  border: 1px solid #e4e7ed;
}

.server-permissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 8px;
}

.server-permission-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f0f9eb;
  border: 1px solid #e1f3d8;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 0.9em;
  color: #67c23a;
}

.server-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.server-name {
  flex-grow: 1;
}

.server-details {
  font-size: 0.8em;
  color: #909399;
}

.detail-item {
  margin-right: 8px;
}

/* 空状态 */
.empty-state {
  margin-top: 20px;
}

/* flex间距 */
.space-x-2 > * + * {
  margin-left: 8px;
}

/* 运营小组区域样式 */
.operation-teams {
  min-height: 32px;
  margin-top: 12px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

/* 运营小组权限列表样式 */
.operation-team-permissions-list {
  background: white;
  border-radius: 6px;
  padding: 12px;
  margin-top: 16px;
  border: 1px solid #e4e7ed;
}

.operation-team-permissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 8px;
}

.operation-team-permission-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fdf5e6;
  border: 1px solid #f4e2c1;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 0.9em;
  color: #e6a23c;
}

.team-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.team-name {
  font-weight: 500;
}

.team-details {
  font-size: 0.8em;
  color: #909399;
}

/* 响应式网格 */
.grid {
  display: grid;
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.gap-2 {
  gap: 8px;
}

/* 文本省略 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 覆盖element-plus的tab样式 */
:deep(.el-tabs__content) {
  padding: 16px 0;
}

:deep(.el-tabs__item) {
  font-weight: 500;
}

/* 多选标签样式优化 */
:deep(.el-select .el-tag) {
  margin: 2px;
}

/* 表单项间距 */
.el-form-item {
  margin-bottom: 18px;
}

/* 卡片样式 */
.el-tab-pane {
  padding: 0;
}

/* 滚动条样式 */
.max-h-48::-webkit-scrollbar {
  width: 6px;
}

.max-h-48::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.max-h-48::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.max-h-48::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style> 