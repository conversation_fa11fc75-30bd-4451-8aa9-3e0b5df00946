<template>
  <div class="team-leader-performance">
    <!-- 组内总览 -->
    <el-card shadow="hover" class="overview-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <Icon icon="ep:user-filled" class="header-icon" />
            <span class="card-title">我的团队总览</span>
          </div>
          <div class="header-right">
            <el-tag type="warning" size="large">
              组长视图 - {{ formatDateRange(props.filterParams.dateRange) }}
            </el-tag>
          </div>
        </div>
      </template>

      <div class="overview-content">
        <div class="overview-stats">
          <div class="stat-item primary">
            <div class="stat-icon">
              <Icon icon="ep:money" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{
                formatCurrency(performanceData.departmentTotal || 0)
              }}</div>
              <div class="stat-label">团队总业绩</div>
            </div>
          </div>

          <div class="stat-item success">
            <div class="stat-icon">
              <Icon icon="ep:box" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ getProductCount() }}</div>
              <div class="stat-label">涉及产品</div>
            </div>
          </div>

          <div class="stat-item warning">
            <div class="stat-icon">
              <Icon icon="ep:user" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ getTeamMemberCount() }}</div>
              <div class="stat-label">团队人数</div>
            </div>
          </div>

          <div class="stat-item info">
            <div class="stat-icon">
              <Icon icon="ep:trophy" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ formatCurrency(getTeamAverage()) }}</div>
              <div class="stat-label">人均业绩</div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 按产品分组的团队业绩 -->
    <div v-if="performanceData.productBreakdown && performanceData.productBreakdown.length > 0">
      <el-card
        v-for="product in performanceData.productBreakdown"
        :key="product.productId"
        shadow="hover"
        class="product-card"
      >
        <template #header>
          <div class="card-header">
            <div class="header-left">
              <Icon icon="ep:goods" class="header-icon product-icon" />
              <span class="card-title">{{ product.productName }}</span>
              <el-tag type="success" size="small" class="product-amount">
                {{ formatCurrency(product.totalAmount) }}
              </el-tag>
            </div>
            <div class="header-right">
              <el-button size="small" @click="toggleProductExpansion(product.productId)">
                {{ expandedProducts.includes(product.productId) ? '收起' : '展开成员' }}
              </el-button>
            </div>
          </div>
        </template>

        <div class="product-content">
          <!-- 产品业绩概览 -->
          <div class="product-overview">
            <div class="product-stats">
              <div class="product-stat">
                <span class="stat-label">产品业绩</span>
                <span class="stat-value">{{ formatCurrency(product.totalAmount) }}</span>
              </div>
              <div class="product-stat">
                <span class="stat-label">参与人数</span>
                <span class="stat-value">{{ getProductMemberCount(product) }}人</span>
              </div>
              <div class="product-stat">
                <span class="stat-label">人均业绩</span>
                <span class="stat-value">{{ formatCurrency(getProductAverage(product)) }}</span>
              </div>
              <div class="product-stat">
                <span class="stat-label">占团队比例</span>
                <span class="stat-value">{{ getProductTeamPercentage(product.totalAmount) }}%</span>
              </div>
            </div>
          </div>

          <!-- 成员详情 -->
          <div v-show="expandedProducts.includes(product.productId)" class="members-detail">
            <el-table :data="getProductMembers(product)" border :row-class-name="getRowClassName">
              <el-table-column label="排名" width="80" align="center">
                <template #default="{ $index }">
                  <div class="rank-badge">
                    <el-tag :type="getRankTagType($index + 1)" size="large" effect="dark">
                      {{ $index + 1 }}
                    </el-tag>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="成员信息" min-width="150">
                <template #default="{ row }">
                  <div class="member-info">
                    <div class="member-avatar">
                      <Icon icon="ep:user" />
                    </div>
                    <div class="member-details">
                      <div class="member-name">
                        {{ row.userName }}
                        <el-tag v-if="row.isLeader" type="warning" size="small">组长</el-tag>
                      </div>
                      <div class="member-id">ID: {{ row.userId }}</div>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="业绩金额" align="right" width="150" sortable prop="amount">
                <template #default="{ row }">
                  <div class="amount-cell">
                    <span class="amount-text">{{ formatCurrency(row.amount) }}</span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="占产品比例" align="center" width="120">
                <template #default="{ row }">
                  <span class="product-percentage">
                    {{ getProductMemberPercentage(row.amount, product.totalAmount) }}%
                  </span>
                </template>
              </el-table-column>

              <el-table-column label="操作" align="center" width="100">
                <template #default="{ row }">
                  <el-button type="text" size="small" @click="viewMemberDetail(row)">
                    查看详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 我的个人业绩 -->
    <el-card shadow="hover" class="personal-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <Icon icon="ep:user" class="header-icon" />
            <span class="card-title">我的个人业绩</span>
          </div>
          <div class="header-right">
            <el-tag :type="getPersonalRankTagType()" size="large">
              组内排名第{{ getPersonalRank() }}名
            </el-tag>
          </div>
        </div>
      </template>

      <div class="personal-content">
        <div class="personal-stats">
          <div class="personal-main">
            <div class="personal-amount">
              <span class="amount-label">个人业绩</span>
              <span class="amount-value">{{ formatCurrency(getPersonalAmount()) }}</span>
            </div>
            <div class="personal-progress">
              <div class="progress-label">
                <span>目标完成度</span>
                <span>{{ getCompletionRate() }}%</span>
              </div>
              <el-progress
                :percentage="getCompletionRate()"
                :stroke-width="12"
                :color="getProgressColor(getCompletionRate())"
              />
            </div>
          </div>
          <div class="personal-chart">
            <div ref="personalChartContainer" class="mini-chart"></div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 团队成员排名 -->
    <el-card shadow="hover" class="members-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <Icon icon="ep:list" class="header-icon" />
            <span class="card-title">团队成员排名</span>
          </div>
          <div class="header-right">
            <el-button-group size="small">
              <el-button :type="sortBy === 'amount' ? 'primary' : ''" @click="sortBy = 'amount'">
                按业绩排序
              </el-button>
              <el-button :type="sortBy === 'rate' ? 'primary' : ''" @click="sortBy = 'rate'">
                按完成率排序
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <div class="members-content">
        <el-table
          :data="sortedMembers"
          v-loading="loading"
          border
          :row-class-name="getRowClassName"
        >
          <el-table-column label="排名" width="80" align="center">
            <template #default="{ $index }">
              <div class="rank-badge">
                <el-tag :type="getRankTagType($index + 1)" size="large" effect="dark">
                  {{ $index + 1 }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="成员信息" min-width="150">
            <template #default="{ row }">
              <div class="member-info">
                <div class="member-avatar">
                  <Icon icon="ep:user" />
                </div>
                <div class="member-details">
                  <div class="member-name">
                    {{ row.userName }}
                    <el-tag v-if="row.isLeader" type="warning" size="small">组长</el-tag>
                  </div>
                  <div class="member-id">ID: {{ row.userId }}</div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="业绩金额" align="right" width="150" sortable prop="amount">
            <template #default="{ row }">
              <div class="amount-cell">
                <span class="amount-text">{{ formatCurrency(row.amount) }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            label="目标完成率"
            align="center"
            width="150"
            sortable
            prop="completionRate"
          >
            <template #default="{ row }">
              <div class="completion-cell">
                <el-progress
                  :percentage="row.completionRate || 0"
                  :stroke-width="8"
                  :show-text="false"
                  :color="getProgressColor(row.completionRate || 0)"
                />
                <span class="completion-text">{{ row.completionRate || 0 }}%</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="占组比例" align="center" width="120">
            <template #default="{ row }">
              <span class="team-percentage"> {{ getTeamPercentage(row.amount) }}% </span>
            </template>
          </el-table-column>

          <el-table-column label="月度趋势" align="center" width="100">
            <template #default="{ row }">
              <div class="trend-indicator">
                <el-icon :class="getTrendClass(row.trend)" :size="20">
                  <ArrowUp v-if="row.trend > 0" />
                  <ArrowDown v-else-if="row.trend < 0" />
                  <Minus v-else />
                </el-icon>
                <span class="trend-text">{{ getTrendText(row.trend) }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" width="100">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewMemberDetail(row)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 团队业绩图表 -->
    <el-card shadow="hover" class="chart-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <Icon icon="ep:trend-charts" class="header-icon" />
            <span class="card-title">团队业绩分析</span>
          </div>
          <div class="header-right">
            <el-radio-group v-model="chartType" size="small">
              <el-radio-button label="distribution">分布图</el-radio-button>
              <el-radio-button label="trend">趋势图</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>

      <div class="chart-content">
        <div ref="teamChartContainer" class="team-chart"></div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'

interface Props {
  performanceData: any
  filterParams: {
    dateRange: string[]
    productIds: number[]
    dimension: string
  }
}

interface Emits {
  (e: 'expand', type: string, id: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const loading = ref(false)
const sortBy = ref<'amount' | 'rate'>('amount')
const chartType = ref<'distribution' | 'trend'>('distribution')
const expandedProducts = ref<number[]>([])
const personalChartContainer = ref()
const teamChartContainer = ref()
let personalChart: echarts.ECharts | null = null
let teamChart: echarts.ECharts | null = null

// 模拟当前用户是组长
const currentUserId = ref('3') // 假设当前用户ID为3

/** 格式化金额 */
const formatCurrency = (amount: number) => {
  return amount.toLocaleString()
}

/** 格式化日期范围 */
const formatDateRange = (dateRange: string[]) => {
  if (!dateRange || dateRange.length === 0) return '未选择日期'
  if (dateRange.length === 1 || dateRange[0] === dateRange[1]) {
    return dateRange[0]
  }
  return `${dateRange[0]} 至 ${dateRange[1]}`
}

/** 获取产品数量 */
const getProductCount = () => {
  return props.performanceData.productBreakdown?.length || 0
}

/** 获取团队人数 */
const getTeamMemberCount = () => {
  if (!props.performanceData.productBreakdown) return 0
  return props.performanceData.productBreakdown.reduce((total, product) => {
    return total + (product.teamData?.[0]?.memberCount || 0)
  }, 0)
}

/** 获取团队平均业绩 */
const getTeamAverage = () => {
  const total = props.performanceData.departmentTotal || 0
  const count = getTeamMemberCount()
  return count > 0 ? Math.round(total / count) : 0
}

/** 获取产品成员数量 */
const getProductMemberCount = (product: any) => {
  return product.teamData?.[0]?.memberCount || 0
}

/** 获取产品平均业绩 */
const getProductAverage = (product: any) => {
  const memberCount = getProductMemberCount(product)
  return memberCount > 0 ? Math.round(product.totalAmount / memberCount) : 0
}

/** 获取产品占团队比例 */
const getProductTeamPercentage = (productAmount: number) => {
  if (!props.performanceData.departmentTotal) return 0
  return Math.round((productAmount / props.performanceData.departmentTotal) * 100)
}

/** 获取产品成员数据 */
const getProductMembers = (product: any) => {
  const members = product.teamData?.[0]?.members || []
  // 添加组长标识并排序
  return members
    .map((member: any) => ({
      ...member,
      isLeader: member.userId === currentUserId.value
    }))
    .sort((a: any, b: any) => b.amount - a.amount)
}

/** 获取成员在产品中的占比 */
const getProductMemberPercentage = (memberAmount: number, productTotal: number) => {
  if (productTotal === 0) return 0
  return ((memberAmount / productTotal) * 100).toFixed(1)
}

/** 切换产品展开状态 */
const toggleProductExpansion = (productId: number) => {
  const index = expandedProducts.value.indexOf(productId)
  if (index > -1) {
    expandedProducts.value.splice(index, 1)
  } else {
    expandedProducts.value.push(productId)
  }
}

/** 获取个人业绩 */
const getPersonalAmount = () => {
  const members = props.performanceData.teamData?.[0]?.members || []
  const personal = members.find((m) => m.userId === currentUserId.value)
  return personal?.amount || 0
}

/** 获取个人排名 */
const getPersonalRank = () => {
  const members = [...(props.performanceData.teamData?.[0]?.members || [])]
  members.sort((a, b) => b.amount - a.amount)
  const index = members.findIndex((m) => m.userId === currentUserId.value)
  return index >= 0 ? index + 1 : '-'
}

/** 获取个人排名标签类型 */
const getPersonalRankTagType = () => {
  const rank = getPersonalRank()
  if (rank === 1) return 'danger'
  if (rank === 2) return 'warning'
  if (rank === 3) return 'success'
  return 'info'
}

/** 获取完成率 */
const getCompletionRate = () => {
  // 模拟数据，实际应该从API获取
  const target = 500000 // 假设目标业绩50万
  const actual = getPersonalAmount()
  return Math.min(Math.round((actual / target) * 100), 100)
}

/** 获取进度条颜色 */
const getProgressColor = (percentage: number) => {
  if (percentage >= 90) return '#67c23a'
  if (percentage >= 70) return '#e6a23c'
  if (percentage >= 50) return '#f56c6c'
  return '#909399'
}

/** 排序后的成员数据 */
const sortedMembers = computed(() => {
  const members = [...(props.performanceData.teamData?.[0]?.members || [])]
  // 添加组长标识
  members.forEach((member) => {
    member.isLeader = member.userId === currentUserId.value
    member.completionRate = Math.floor(Math.random() * 100) + 20 // 模拟完成率
    member.trend = Math.random() * 40 - 20 // 模拟趋势
  })

  if (sortBy.value === 'amount') {
    return members.sort((a, b) => b.amount - a.amount)
  } else {
    return members.sort((a, b) => (b.completionRate || 0) - (a.completionRate || 0))
  }
})

/** 获取排名标签类型 */
const getRankTagType = (rank: number) => {
  if (rank === 1) return 'danger'
  if (rank === 2) return 'warning'
  if (rank === 3) return 'success'
  return 'info'
}

/** 获取行样式类名 */
const getRowClassName = ({ row }: { row: any }) => {
  if (row.isLeader) return 'leader-row'
  return ''
}

/** 获取团队总业绩 */
const getTeamTotal = () => {
  const members = props.performanceData.teamData?.[0]?.members || []
  return members.reduce((total, member) => total + (member.amount || 0), 0)
}

/** 获取团队占比 */
const getTeamPercentage = (memberAmount: number) => {
  const total = getTeamTotal()
  if (total === 0) return 0
  return ((memberAmount / total) * 100).toFixed(1)
}

/** 获取趋势样式类 */
const getTrendClass = (trend: number) => {
  if (trend > 0) return 'trend-up'
  if (trend < 0) return 'trend-down'
  return 'trend-neutral'
}

/** 获取趋势文本 */
const getTrendText = (trend: number) => {
  if (trend > 0) return '上升'
  if (trend < 0) return '下降'
  return '持平'
}

/** 查看成员详情 */
const viewMemberDetail = (member: any) => {
  ElMessage.info(`查看 ${member.userName} 的详细业绩数据`)
  emit('expand', 'member', member.userId)
}

/** 初始化个人图表 */
const initPersonalChart = () => {
  if (!personalChartContainer.value) return

  if (personalChart) {
    personalChart.dispose()
  }

  personalChart = echarts.init(personalChartContainer.value)

  const option = {
    grid: {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0
    },
    xAxis: {
      type: 'category',
      show: false,
      data: ['1周', '2周', '3周', '4周']
    },
    yAxis: {
      type: 'value',
      show: false
    },
    series: [
      {
        data: [120000, 150000, 180000, getPersonalAmount()],
        type: 'line',
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: '#409EFF',
          width: 3
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
          ])
        }
      }
    ]
  }

  personalChart.setOption(option)
}

/** 初始化团队图表 */
const initTeamChart = () => {
  if (!teamChartContainer.value) return

  if (teamChart) {
    teamChart.dispose()
  }

  teamChart = echarts.init(teamChartContainer.value)
  updateTeamChart()
}

/** 更新团队图表 */
const updateTeamChart = () => {
  if (!teamChart || !sortedMembers.value.length) return

  if (chartType.value === 'distribution') {
    // 业绩分布饼图
    const option = {
      title: {
        text: '团队业绩分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: sortedMembers.value.map((m) => m.userName)
      },
      series: [
        {
          name: '个人业绩',
          type: 'pie',
          radius: '50%',
          data: sortedMembers.value.map((member) => ({
            value: member.amount,
            name: member.userName,
            itemStyle: {
              color: member.isLeader ? '#e6a23c' : undefined
            }
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    teamChart.setOption(option)
  } else {
    // 业绩趋势柱状图
    const option = {
      title: {
        text: '成员业绩对比',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: sortedMembers.value.map((m) => m.userName),
        axisTick: {
          alignWithLabel: true
        },
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '业绩金额',
          type: 'bar',
          barWidth: '60%',
          data: sortedMembers.value.map((member) => ({
            value: member.amount,
            itemStyle: {
              color: member.isLeader ? '#e6a23c' : '#5470c6'
            }
          }))
        }
      ]
    }
    teamChart.setOption(option)
  }
}

// 监听图表类型变化
watch(chartType, () => {
  updateTeamChart()
})

// 监听数据和筛选参数变化
watch(
  [() => props.performanceData, () => props.filterParams],
  () => {
    nextTick(() => {
      initPersonalChart()
      updateTeamChart()
    })
  },
  { deep: true }
)

onMounted(() => {
  nextTick(() => {
    initPersonalChart()
    initTeamChart()
  })
})
</script>

<style scoped>
.team-leader-performance {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  font-size: 18px;
  color: var(--el-color-warning);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 总览样式 */
.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid;
  background: var(--el-bg-color-page);
}

.stat-item.primary {
  border-left-color: var(--el-color-primary);
}
.stat-item.success {
  border-left-color: var(--el-color-success);
}
.stat-item.warning {
  border-left-color: var(--el-color-warning);
}
.stat-item.info {
  border-left-color: var(--el-color-info);
}

.stat-icon {
  font-size: 28px;
  margin-right: 15px;
  color: var(--el-color-warning);
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

/* 个人业绩样式 */
.personal-content {
  padding: 10px 0;
}

.personal-stats {
  display: flex;
  gap: 30px;
  align-items: center;
}

.personal-main {
  flex: 1;
}

.personal-amount {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.amount-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}

.amount-value {
  font-size: 32px;
  font-weight: bold;
  color: var(--el-color-warning);
}

.personal-progress {
  margin-top: 15px;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.personal-chart {
  width: 200px;
}

.mini-chart {
  width: 100%;
  height: 100px;
}

/* 产品卡片样式 */
.product-card {
  margin-bottom: 20px;
}

.product-icon {
  color: var(--el-color-success);
}

.product-amount {
  margin-left: 10px;
}

.product-content {
  margin-top: 15px;
}

.product-overview {
  background: var(--el-bg-color-page);
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.product-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.product-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.product-stat .stat-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}

.product-stat .stat-value {
  font-size: 18px;
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.members-detail {
  margin-top: 15px;
}

.product-percentage {
  color: var(--el-text-color-regular);
  font-weight: 500;
}

/* 成员表格样式 */
.members-content {
  margin-top: 15px;
}

:deep(.leader-row) {
  background-color: rgba(230, 162, 60, 0.1);
}

.rank-badge {
  display: flex;
  justify-content: center;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--el-color-primary-light-9);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--el-color-primary);
}

.member-details {
  flex: 1;
}

.member-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.member-id {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}

.amount-cell .amount-text {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-color-primary);
}

.completion-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.completion-text {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.team-percentage {
  color: var(--el-text-color-regular);
  font-weight: 500;
}

.trend-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.trend-text {
  font-size: 12px;
}

.trend-up {
  color: var(--el-color-success);
}

.trend-down {
  color: var(--el-color-danger);
}

.trend-neutral {
  color: var(--el-text-color-placeholder);
}

/* 图表样式 */
.chart-content {
  margin-top: 15px;
}

.team-chart {
  width: 100%;
  height: 400px;
}
</style>
