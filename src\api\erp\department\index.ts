import request from '@/config/axios'

// 部门 VO
export interface DepartmentVO {
  id?: number
  parentId?: number
  deptName?: string
  deptCode?: string
  deptLevel?: number
  deptType?: string  // 部门业务类型：OPERATION-运营部门，OPS-运维部门
  sortOrder?: number
  status?: number
  description?: string
  createTime?: string | number
  updateTime?: string | number
  children?: DepartmentVO[]
}

// 部门页面查询 VO
export interface DepartmentPageReqVO {
  pageNo?: number
  pageSize?: number
  deptName?: string
  deptCode?: string
  deptLevel?: number
  status?: number
  parentId?: number
}

// 部门新增/修改 VO
export interface DepartmentSaveReqVO {
  id?: number
  parentId?: number
  deptName?: string
  deptCode?: string
  deptLevel?: number
  deptType?: string  // 部门业务类型：OPERATION-运营部门，OPS-运维部门
  sortOrder?: number
  status?: number
  description?: string
}

// 用户部门关系 VO
export interface UserDeptRelationVO {
  id?: number
  userId?: number
  deptId?: number
  relationType?: number
  role?: string  // 用户在部门中的角色：SUPERVISOR、TEAM_LEADER、TEAM_MEMBER
  status?: number
  createTime?: string | number
  // 扩展字段
  username?: string  // 兼容字段
  nickname?: string
  userName?: string
}

// 用户资源绑定 VO
export interface UserResourceBindingVO {
  id?: number
  userId: number
  deptId: number
  resourceType?: string
  productId?: number
  productIds?: number[]
  channelCode?: string
  channelCodes?: string[]
  targetDeptId?: number
  operationTeam?: string
  teams?: string[]
  serverName?: string
  serverNames?: string[]
  status: number
  remark?: string
  createTime?: string | number
  updateTime?: string | number
}

// 用户资源绑定请求 VO
export interface UserResourceBindingReqVO {
  userId: number
  deptId: number
  resourceType?: string
  productIds?: number[]
  channelCodes?: string[]
  targetDeptId?: number
  teams?: string[]
  serverNames?: string[]
  bindingMode?: string // 绑定模式：ONE_TO_ONE-一一对应，CARTESIAN-笛卡尔积全组合
  operationMode?: string // 操作模式：REPLACE-替换所有绑定，ADD-增量添加绑定，UPDATE-更新绑定，DELETE-删除绑定
  bindingItems?: ProductChannelBindingItem[] // 详细的产品渠道绑定项
  status: number
  remark?: string
}

// 产品渠道绑定项
export interface ProductChannelBindingItem {
  productId: number
  channelCode: string
  productName?: string
  channelName?: string
}

// 运维部门专用请求 VO
export interface OpsUserResourceBindingReqVO {
  userId: number
  deptId: number
  targetDeptId?: number
  teams?: string[]
  productIds?: number[]
  serverNames?: string[]
  bindingMode?: string // 绑定模式：ONE_TO_ONE-一一对应，CARTESIAN-笛卡尔积全组合
  operationMode?: string // 操作模式：REPLACE-替换所有绑定，ADD-增量添加绑定，UPDATE-更新绑定，DELETE-删除绑定
  bindingItems?: ProductServerBindingItem[] // 详细的产品区服绑定项
  status: number
  remark?: string
}

// 产品区服绑定项
export interface ProductServerBindingItem {
  productId: number
  serverName: string
  productName?: string
}

// 批量资源绑定操作请求 VO
export interface BatchResourceBindingReqVO {
  userId: number
  operationMode?: string // 操作模式：REPLACE-替换所有绑定，ADD-增量添加绑定，UPDATE-更新绑定，DELETE-删除绑定
  bindings: ResourceBindingItemVO[]
}

// 资源绑定项 VO
export interface ResourceBindingItemVO {
  id?: number // 绑定ID，更新/删除时需要
  opsDeptId?: number
  resourceType?: string
  targetDeptId?: number
  productId?: number
  channelCode?: string
  operationTeam?: string
  serverName?: string
  selectedProductIds?: number[]
  selectedChannelCodes?: string[]
  operationTeams?: string[]
  serverNames?: string[]
  status?: number
  remark?: string
}

// 部门 API
export const DepartmentApi = {
  // 查询部门分页
  getDepartmentPage: async (params: DepartmentPageReqVO) => {
    return await request.get({ url: `/erp/department/page`, params })
  },

  // 查询部门树形结构
  getDepartmentTree: async () => {
    return await request.get({ url: `/erp/department/tree` })
  },

  // 查询部门详情
  getDepartment: async (id: number) => {
    return await request.get({ url: `/erp/department/get?id=` + id })
  },

  // 新增部门
  createDepartment: async (data: DepartmentSaveReqVO) => {
    return await request.post({ url: `/erp/department/create`, data })
  },

  // 修改部门
  updateDepartment: async (data: DepartmentSaveReqVO) => {
    return await request.put({ url: `/erp/department/update`, data })
  },

  // 删除部门
  deleteDepartment: async (id: number) => {
    return await request.delete({ url: `/erp/department/delete?id=` + id })
  },

  // 查询用户部门关系列表
  getUserDeptRelations: async (userId: number) => {
    return await request.get({ url: `/erp/department/user-relations?userId=` + userId })
  },

  // 分配用户到部门
  assignUserToDepartment: async (data: { userId: number; deptIds: number[]; relationType?: number; role?: string }) => {
    return await request.post({ url: `/erp/department/assign-user`, data })
  },

  // 移除用户部门关系
  removeUserFromDepartment: async (userId: number, deptId: number) => {
    return await request.delete({ url: `/erp/department/remove-user-from-department?userId=${userId}&deptId=${deptId}` })
  },

  // 获取部门下的用户列表
  getDepartmentUsers: async (deptId: number) => {
    return await request.get({ url: `/erp/department/users?deptId=` + deptId })
  },

  // 保存运营部门用户资源绑定
  saveUserResourceBinding: async (data: UserResourceBindingReqVO) => {
    return await request.post({ url: `/erp/user-resource/save-operation-binding`, data })
  },

  // 保存运维部门用户资源绑定
  saveOpsUserResourceBinding: async (data: OpsUserResourceBindingReqVO) => {
    return await request.post({ url: `/erp/user-resource/save-ops-binding`, data })
  },

  // 批量操作用户资源绑定（支持指定操作模式）
  batchOperationBinding: async (data: BatchResourceBindingReqVO) => {
    return await request.post({ url: `/erp/user-resource/batch-operation`, data })
  },

  // 替换用户所有资源绑定
  replaceAllBindings: async (data: BatchResourceBindingReqVO) => {
    return await request.post({ url: `/erp/user-resource/replace-all-bindings`, data })
  },

  // 获取用户资源绑定列表
  getUserResourceBindings: async (userId: number) => {
    return await request.get({ url: `/erp/resource-binding/user-bindings?userId=` + userId })
  },

  // 获取用户可访问的资源绑定
  getAccessibleBindings: async (userId: number) => {
    return await request.get({ url: `/erp/resource-binding/accessible-bindings?userId=` + userId })
  },

  // 获取用户可访问的产品ID列表
  getUserBoundProductIds: async (userId: number) => {
    return await request.get({ url: `/erp/resource-binding/products?userId=` + userId })
  },

  // 获取用户可访问的渠道代码列表
  getUserBoundChannelCodes: async (userId: number) => {
    return await request.get({ url: `/erp/resource-binding/channels?userId=` + userId })
  },

  // 获取用户可访问的运营队伍列表
  getUserBoundOperationTeams: async (userId: number) => {
    return await request.get({ url: `/erp/resource-binding/operation-teams?userId=` + userId })
  },

  // 获取用户可访问的区服列表
  getUserBoundServerNames: async (userId: number) => {
    return await request.get({ url: `/erp/resource-binding/servers?userId=` + userId })
  },

  // 测试用户权限
  testUserPermission: async (userId: number, resourceType: string, resourceValue: string) => {
    return await request.get({ 
      url: `/erp/resource-binding/test-permission`,
      params: { userId, resourceType, resourceValue }
    })
  },

  // 删除用户资源绑定
  deleteUserResourceBinding: async (id: number) => {
    return await request.delete({ url: `/erp/resource-binding/delete?id=` + id })
  }
} 