<template>
  <div class="personal-performance">
    <!-- 个人业绩总览 -->
    <el-card shadow="hover" class="overview-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <Icon icon="ep:user" class="header-icon" />
            <span class="card-title">我的业绩总览</span>
          </div>
          <div class="header-right">
            <el-tag type="success" size="large">
              个人视图 - {{ formatDateRange(props.filterParams.dateRange) }}
            </el-tag>
          </div>
        </div>
      </template>

      <div class="overview-content">
        <div class="performance-summary">
          <div class="main-performance">
            <div class="performance-amount">
              <div class="amount-wrapper">
                <Icon icon="ep:money" class="amount-icon" />
                <div class="amount-content">
                  <span class="amount-label">总业绩</span>
                  <span class="amount-value">{{ formatCurrency(getTotalAmount()) }}</span>
                </div>
              </div>
            </div>

            <div class="performance-target">
              <div class="target-info">
                <div class="target-details">
                  <span class="target-label">涉及产品</span>
                  <span class="target-value">{{ getProductCount() }}个</span>
                </div>
                <div class="completion-rate">
                  <span class="rate-text rate-excellent">
                    {{ getProductCount() > 0 ? '多元化' : '单一化' }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="performance-chart">
            <div ref="trendChartContainer" class="trend-chart"></div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 按产品分组的个人业绩 -->
    <div v-if="performanceData.productBreakdown && performanceData.productBreakdown.length > 0">
      <el-card
        v-for="product in performanceData.productBreakdown"
        :key="product.productId"
        shadow="hover"
        class="product-card"
      >
        <template #header>
          <div class="card-header">
            <div class="header-left">
              <Icon icon="ep:goods" class="header-icon product-icon" />
              <span class="card-title">{{ product.productName }}</span>
              <el-tag type="success" size="small" class="product-amount">
                {{ formatCurrency(product.totalAmount) }}
              </el-tag>
            </div>
            <div class="header-right">
              <el-tag :type="getProductRankTagType(product)" size="small">
                排名第{{ getProductRank(product) }}名
              </el-tag>
            </div>
          </div>
        </template>

        <div class="product-content">
          <!-- 产品业绩详情 -->
          <div class="product-overview">
            <div class="product-stats">
              <div class="product-stat">
                <span class="stat-label">我的业绩</span>
                <span class="stat-value">{{ formatCurrency(product.totalAmount) }}</span>
              </div>
              <div class="product-stat">
                <span class="stat-label">目标业绩</span>
                <span class="stat-value">{{ formatCurrency(getProductTarget(product)) }}</span>
              </div>
              <div class="product-stat">
                <span class="stat-label">完成率</span>
                <span class="stat-value">{{ getProductCompletionRate(product) }}%</span>
              </div>
              <div class="product-stat">
                <span class="stat-label">占总业绩</span>
                <span class="stat-value"
                  >{{ getProductTotalPercentage(product.totalAmount) }}%</span
                >
              </div>
            </div>
          </div>

          <!-- 产品业绩进度 -->
          <div class="product-progress">
            <div class="progress-label">
              <span>{{ product.productName }}完成度</span>
              <span>{{ getProductCompletionRate(product) }}%</span>
            </div>
            <el-progress
              :percentage="getProductCompletionRate(product)"
              :stroke-width="12"
              :color="getProgressColor(getProductCompletionRate(product))"
            />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 业绩详细分析 -->
    <el-card shadow="hover" class="analysis-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <Icon icon="ep:data-analysis" class="header-icon" />
            <span class="card-title">业绩分析</span>
          </div>
          <div class="header-right">
            <el-button-group size="small">
              <el-button
                :type="analysisType === 'weekly' ? 'primary' : ''"
                @click="analysisType = 'weekly'"
              >
                周度分析
              </el-button>
              <el-button
                :type="analysisType === 'monthly' ? 'primary' : ''"
                @click="analysisType = 'monthly'"
              >
                月度分析
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <div class="analysis-content">
        <div class="analysis-stats">
          <div class="stat-grid">
            <div class="stat-card best">
              <div class="stat-header">
                <Icon icon="ep:star-filled" class="stat-icon" />
                <span class="stat-title">最佳表现</span>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ getBestWeek() }}</div>
                <div class="stat-description">{{ formatCurrency(getBestWeekAmount()) }}</div>
              </div>
            </div>

            <div class="stat-card average">
              <div class="stat-header">
                <Icon icon="ep:data-line" class="stat-icon" />
                <span class="stat-title">周均业绩</span>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatCurrency(getWeeklyAverage()) }}</div>
                <div class="stat-description">较上月{{ getAverageChange() }}</div>
              </div>
            </div>

            <div class="stat-card rank">
              <div class="stat-header">
                <Icon icon="ep:trophy" class="stat-icon" />
                <span class="stat-title">团队排名</span>
              </div>
              <div class="stat-content">
                <div class="stat-value">第{{ getTeamRank() }}名</div>
                <div class="stat-description">共{{ getTeamMemberCount() }}人</div>
              </div>
            </div>

            <div class="stat-card growth">
              <div class="stat-header">
                <Icon icon="ep:trend-charts" class="stat-icon" />
                <span class="stat-title">增长趋势</span>
              </div>
              <div class="stat-content">
                <div class="stat-value" :class="getGrowthClass()">
                  {{ getGrowthRate() }}
                </div>
                <div class="stat-description">环比上月</div>
              </div>
            </div>
          </div>
        </div>

        <div class="analysis-chart">
          <div ref="analysisChartContainer" class="analysis-chart-container"></div>
        </div>
      </div>
    </el-card>

    <!-- 业绩明细 -->
    <el-card shadow="hover" class="details-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <Icon icon="ep:list" class="header-icon" />
            <span class="card-title">业绩明细</span>
          </div>
          <div class="header-right">
            <el-button size="small" @click="refreshDetails" :loading="detailLoading">
              <Icon icon="ep:refresh" class="mr-1" />
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <div class="details-content">
        <el-table
          :data="performanceDetails"
          v-loading="detailLoading"
          border
          stripe
          :summary-method="getSummaries"
          show-summary
          summary-text="总计"
        >
          <el-table-column label="日期" prop="date" width="120" align="center">
            <template #default="{ row }">
              <span class="date-text">{{ formatDate(row.date) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="业绩类型" prop="type" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getTypeTagType(row.type)" size="small">
                {{ getTypeText(row.type) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="金额" prop="amount" width="120" align="right">
            <template #default="{ row }">
              <span class="amount-detail">{{ formatCurrency(row.amount) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="客户信息" prop="customer" min-width="120">
            <template #default="{ row }">
              <div class="customer-info">
                <Icon icon="ep:user" class="customer-icon" />
                <span>{{ row.customer || '系统收入' }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="产品" prop="product" width="100" align="center">
            <template #default="{ row }">
              <span class="product-text">{{ row.product }}</span>
            </template>
          </el-table-column>

          <el-table-column label="备注" prop="remark" min-width="150">
            <template #default="{ row }">
              <span class="remark-text">{{ row.remark || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column label="状态" prop="status" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="detailPageParams.pageNo"
            v-model:page-size="detailPageParams.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="detailTotal"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleDetailPageSizeChange"
            @current-change="handleDetailPageChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'

interface Props {
  performanceData: any
  filterParams: {
    dateRange: string[]
    productIds: number[]
    dimension: string
  }
}

const props = defineProps<Props>()

const analysisType = ref<'weekly' | 'monthly'>('weekly')
const detailLoading = ref(false)
const trendChartContainer = ref()
const analysisChartContainer = ref()
let trendChart: echarts.ECharts | null = null
let analysisChart: echarts.ECharts | null = null

// 分页参数
const detailPageParams = reactive({
  pageNo: 1,
  pageSize: 20
})
const detailTotal = ref(0)

// 模拟个人业绩数据
const personalData = ref({
  amount: 280000,
  target: 500000,
  teamRank: 2,
  teamTotal: 6,
  weeklyData: [45000, 52000, 61000, 68000, 72000],
  monthlyGrowth: 15.8,
  bestWeek: { week: '第4周', amount: 72000 }
})

// 模拟业绩明细数据
const performanceDetails = ref([
  {
    date: '2024-01-15',
    type: 'sale',
    amount: 12000,
    customer: '客户A',
    product: '游戏1',
    remark: '新用户充值',
    status: 'confirmed'
  },
  {
    date: '2024-01-14',
    type: 'bonus',
    amount: 5000,
    customer: null,
    product: '游戏1',
    remark: '月度奖金',
    status: 'confirmed'
  },
  {
    date: '2024-01-13',
    type: 'sale',
    amount: 8000,
    customer: '客户B',
    product: '游戏2',
    remark: '续费收入',
    status: 'confirmed'
  },
  {
    date: '2024-01-12',
    type: 'commission',
    amount: 3000,
    customer: '客户C',
    product: '游戏1',
    remark: '推荐奖励',
    status: 'pending'
  },
  {
    date: '2024-01-11',
    type: 'sale',
    amount: 15000,
    customer: '客户D',
    product: '游戏1',
    remark: '大客户充值',
    status: 'confirmed'
  }
])

/** 格式化金额 */
const formatCurrency = (amount: number) => {
  // if (amount >= 10000) {
  //   return `${(amount / 10000).toFixed(1)}万`
  // }
  return amount.toLocaleString()
}

/** 格式化日期范围 */
const formatDateRange = (dateRange: string[]) => {
  if (!dateRange || dateRange.length === 0) return '未选择日期'
  if (dateRange.length === 1 || dateRange[0] === dateRange[1]) {
    return dateRange[0]
  }
  return `${dateRange[0]} 至 ${dateRange[1]}`
}

/** 格式化日期 */
const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}/${date.getDate()}`
}

/** 获取产品数量 */
const getProductCount = () => {
  return props.performanceData.productBreakdown?.length || 0
}

/** 获取总业绩 */
const getTotalAmount = () => {
  return props.performanceData.departmentTotal || 0
}

/** 获取产品目标业绩 */
const getProductTarget = (product: any) => {
  return product.personalData?.targetAmount || 100000
}

/** 获取产品完成率 */
const getProductCompletionRate = (product: any) => {
  const target = getProductTarget(product)
  return target > 0 ? Math.min(Math.round((product.totalAmount / target) * 100), 100) : 0
}

/** 获取产品排名 */
const getProductRank = (product: any) => {
  return product.personalData?.rank || 2
}

/** 获取产品排名标签类型 */
const getProductRankTagType = (product: any) => {
  const rank = getProductRank(product)
  if (rank === 1) return 'danger'
  if (rank === 2) return 'warning'
  if (rank === 3) return 'success'
  return 'info'
}

/** 获取产品占总业绩比例 */
const getProductTotalPercentage = (productAmount: number) => {
  const total = getTotalAmount()
  return total > 0 ? Math.round((productAmount / total) * 100) : 0
}

/** 获取进度条颜色 */
const getProgressColor = (percentage: number) => {
  if (percentage >= 90) return '#67c23a'
  if (percentage >= 70) return '#e6a23c'
  if (percentage >= 50) return '#f56c6c'
  return '#909399'
}

/** 获取最佳周 */
const getBestWeek = () => personalData.value.bestWeek.week

/** 获取最佳周金额 */
const getBestWeekAmount = () => personalData.value.bestWeek.amount

/** 获取周均业绩 */
const getWeeklyAverage = () => {
  const total = personalData.value.weeklyData.reduce((sum, val) => sum + val, 0)
  return Math.round(total / personalData.value.weeklyData.length)
}

/** 获取平均变化 */
const getAverageChange = () => {
  const growth = personalData.value.monthlyGrowth
  return growth > 0 ? `上升${growth}%` : `下降${Math.abs(growth)}%`
}

/** 获取团队排名 */
const getTeamRank = () => personalData.value.teamRank

/** 获取团队人数 */
const getTeamMemberCount = () => personalData.value.teamTotal

/** 获取增长率 */
const getGrowthRate = () => {
  const growth = personalData.value.monthlyGrowth
  return growth > 0 ? `+${growth}%` : `${growth}%`
}

/** 获取增长样式类 */
const getGrowthClass = () => {
  const growth = personalData.value.monthlyGrowth
  return growth > 0 ? 'growth-positive' : 'growth-negative'
}

/** 获取类型标签类型 */
const getTypeTagType = (type: string) => {
  switch (type) {
    case 'sale':
      return 'primary'
    case 'bonus':
      return 'success'
    case 'commission':
      return 'warning'
    default:
      return 'info'
  }
}

/** 获取类型文本 */
const getTypeText = (type: string) => {
  switch (type) {
    case 'sale':
      return '销售'
    case 'bonus':
      return '奖金'
    case 'commission':
      return '提成'
    default:
      return '其他'
  }
}

/** 获取状态标签类型 */
const getStatusTagType = (status: string) => {
  return status === 'confirmed' ? 'success' : 'warning'
}

/** 获取状态文本 */
const getStatusText = (status: string) => {
  return status === 'confirmed' ? '已确认' : '待确认'
}

/** 表格合计方法 */
const getSummaries = (param: any) => {
  const { columns, data } = param
  const sums: string[] = []
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '总计'
      return
    }
    if (column.property === 'amount') {
      const values = data.map((item: any) => Number(item[column.property]))
      if (!values.every((value: any) => Number.isNaN(value))) {
        const total = values.reduce((prev: number, curr: number) => {
          const value = Number(curr)
          if (!Number.isNaN(value)) {
            return prev + curr
          } else {
            return prev
          }
        }, 0)
        sums[index] = formatCurrency(total)
      } else {
        sums[index] = 'N/A'
      }
    } else {
      sums[index] = ''
    }
  })
  return sums
}

/** 刷新明细 */
const refreshDetails = async () => {
  detailLoading.value = true
  // 模拟API调用
  setTimeout(() => {
    detailLoading.value = false
  }, 1000)
}

/** 处理明细分页大小变化 */
const handleDetailPageSizeChange = (size: number) => {
  detailPageParams.pageSize = size
  // TODO: 重新加载数据
}

/** 处理明细页码变化 */
const handleDetailPageChange = (page: number) => {
  detailPageParams.pageNo = page
  // TODO: 重新加载数据
}

/** 初始化趋势图表 */
const initTrendChart = () => {
  if (!trendChartContainer.value) return

  if (trendChart) {
    trendChart.dispose()
  }

  trendChart = echarts.init(trendChartContainer.value)

  const option = {
    grid: {
      left: 0,
      right: 0,
      top: 10,
      bottom: 0
    },
    xAxis: {
      type: 'category',
      show: false,
      data: ['第1周', '第2周', '第3周', '第4周', '第5周']
    },
    yAxis: {
      type: 'value',
      show: false
    },
    series: [
      {
        data: personalData.value.weeklyData,
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          color: '#67c23a',
          width: 3
        },
        itemStyle: {
          color: '#67c23a'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
            { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }
          ])
        }
      }
    ]
  }

  trendChart.setOption(option)
}

/** 初始化分析图表 */
const initAnalysisChart = () => {
  if (!analysisChartContainer.value) return

  if (analysisChart) {
    analysisChart.dispose()
  }

  analysisChart = echarts.init(analysisChartContainer.value)
  updateAnalysisChart()
}

/** 更新分析图表 */
const updateAnalysisChart = () => {
  if (!analysisChart) return

  if (analysisType.value === 'weekly') {
    // 周度分析柱状图
    const option = {
      title: {
        text: '周度业绩趋势',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['第1周', '第2周', '第3周', '第4周', '第5周']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '业绩金额',
          type: 'bar',
          data: personalData.value.weeklyData,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#83bff6' },
              { offset: 0.5, color: '#188df0' },
              { offset: 1, color: '#188df0' }
            ])
          }
        }
      ]
    }
    analysisChart.setOption(option)
  } else {
    // 月度分析饼图（业绩构成）
    const monthlyData = [
      { value: 150000, name: '直接销售' },
      { value: 80000, name: '推荐奖励' },
      { value: 30000, name: '月度奖金' },
      { value: 20000, name: '其他收入' }
    ]

    const option = {
      title: {
        text: '月度业绩构成',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: monthlyData.map((item) => item.name)
      },
      series: [
        {
          name: '业绩构成',
          type: 'pie',
          radius: '50%',
          data: monthlyData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    analysisChart.setOption(option)
  }
}

// 监听分析类型变化
watch(analysisType, () => {
  updateAnalysisChart()
})

// 监听筛选参数变化，重新加载相关数据
watch(
  () => props.filterParams,
  () => {
    // TODO: 根据新的筛选参数重新加载个人业绩数据
    console.log('Personal view filter params changed:', props.filterParams)
  },
  { deep: true }
)

onMounted(() => {
  nextTick(() => {
    initTrendChart()
    initAnalysisChart()
  })

  // 模拟设置明细总数
  detailTotal.value = 156
})
</script>

<style scoped>
.personal-performance {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  font-size: 18px;
  color: var(--el-color-success);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 总览样式 */
.overview-content {
  padding: 10px 0;
}

.performance-summary {
  display: flex;
  gap: 30px;
  align-items: stretch;
}

.main-performance {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.performance-amount {
  display: flex;
  align-items: center;
}

.amount-wrapper {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-radius: 16px;
  width: 100%;
}

.amount-icon {
  font-size: 48px;
  color: var(--el-color-success);
}

.amount-content {
  display: flex;
  flex-direction: column;
}

.amount-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}

.amount-value {
  font-size: 36px;
  font-weight: bold;
  color: var(--el-color-success);
}

.performance-target {
  background: var(--el-bg-color-page);
  padding: 20px;
  border-radius: 12px;
}

.target-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.target-details {
  display: flex;
  flex-direction: column;
}

.target-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.target-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.completion-rate .rate-text {
  font-size: 24px;
  font-weight: bold;
}

.rate-excellent {
  color: #67c23a;
}
.rate-good {
  color: #e6a23c;
}
.rate-normal {
  color: #f56c6c;
}
.rate-low {
  color: #909399;
}

.performance-chart {
  width: 300px;
  background: var(--el-bg-color-page);
  border-radius: 12px;
  padding: 20px;
}

.trend-chart {
  width: 100%;
  height: 120px;
}

/* 产品卡片样式 */
.product-card {
  margin-bottom: 20px;
}

.product-icon {
  color: var(--el-color-success);
}

.product-amount {
  margin-left: 10px;
}

.product-content {
  margin-top: 15px;
}

.product-overview {
  background: var(--el-bg-color-page);
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.product-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.product-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.product-stat .stat-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}

.product-stat .stat-value {
  font-size: 18px;
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.product-progress {
  background: var(--el-bg-color-page);
  padding: 15px;
  border-radius: 8px;
}

.product-progress .progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

/* 分析样式 */
.analysis-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stat-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: var(--el-bg-color-page);
  border-radius: 12px;
  padding: 20px;
  border-left: 4px solid;
}

.stat-card.best {
  border-left-color: #f56c6c;
}
.stat-card.average {
  border-left-color: #e6a23c;
}
.stat-card.rank {
  border-left-color: #409eff;
}
.stat-card.growth {
  border-left-color: #67c23a;
}

.stat-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
}

.stat-icon {
  font-size: 20px;
  color: var(--el-color-primary);
}

.stat-title {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.stat-content .stat-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.stat-description {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}

.growth-positive {
  color: #67c23a;
}
.growth-negative {
  color: #f56c6c;
}

.analysis-chart-container {
  width: 100%;
  height: 300px;
}

/* 明细样式 */
.details-content {
  margin-top: 15px;
}

.date-text {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.amount-detail {
  font-weight: 600;
  color: var(--el-color-primary);
}

.customer-info {
  display: flex;
  align-items: center;
  gap: 6px;
}

.customer-icon {
  color: var(--el-color-info);
  font-size: 14px;
}

.product-text,
.remark-text {
  font-size: 13px;
  color: var(--el-text-color-regular);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
