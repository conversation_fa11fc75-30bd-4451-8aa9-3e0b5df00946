<template>
  <Dialog
    v-model="dialogVisible"
    title="业绩查看"
    width="1400px"
    max-height="800px"
  >
    <div class="performance-container">
      <!-- 权限说明和筛选条件 -->
      <el-card shadow="never" class="filter-card">
        <div class="filter-header">
          <div class="user-details">
            <h3>{{ currentUser.userName || '当前用户' }}</h3>
            <el-tag :type="getRoleTagType(userRole)" size="large">
              {{ getRoleText(userRole) }}
            </el-tag>
          </div>
          <p class="role-description">{{ getRoleDescription(userRole) }}</p>
        </div>
        
        <!-- 筛选条件表单 -->
        <el-form
          :model="filterParams"
          ref="filterFormRef"
          :inline="true"
          label-width="80px"
          class="filter-form"
        >
          <el-form-item label="日期范围" prop="dateRange">
            <el-date-picker
              v-model="filterParams.dateRange"
              type="monthrange"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              format="YYYY年MM月"
              value-format="YYYY-MM"
              @change="handleDateRangeChange"
              class="date-picker"
            />
          </el-form-item>
          
          <el-form-item label="产品筛选" prop="productIds">
            <el-select
              v-model="filterParams.productIds"
              placeholder="请选择产品"
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
              @change="handleProductChange"
              class="product-selector"
            >
              <el-option
                v-for="product in productList"
                :key="product.id"
                :label="product.name"
                :value="product.id"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="数据维度" prop="dimension">
            <el-radio-group v-model="filterParams.dimension" @change="handleDimensionChange">
              <el-radio-button label="all">全部</el-radio-button>
              <el-radio-button label="daily">按日</el-radio-button>
              <el-radio-button label="weekly">按周</el-radio-button>
              <el-radio-button label="monthly">按月</el-radio-button>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">
              <Icon icon="ep:search" class="mr-1" />
              查询
            </el-button>
            <el-button @click="resetFilter">
              <Icon icon="ep:refresh" class="mr-1" />
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 业绩视图内容 -->
      <div v-loading="loading" class="performance-content">
        <!-- 超级管理员和部门主管视图 -->
        <template v-if="userRole === 'SUPER_ADMIN' || userRole === 'SUPERVISOR'">
          <SupervisorPerformanceView 
            :performance-data="performanceData"
            :filter-params="filterParams"
            @expand="handleExpand"
          />
        </template>

        <!-- 组长视图 -->
        <template v-else-if="userRole === 'TEAM_LEADER'">
          <TeamLeaderPerformanceView 
            :performance-data="performanceData"
            :filter-params="filterParams"
            @expand="handleExpand"
          />
        </template>

        <!-- 个人视图 -->
        <template v-else-if="userRole === 'TEAM_MEMBER'">
          <PersonalPerformanceView 
            :performance-data="performanceData"
            :filter-params="filterParams"
          />
        </template>

        <!-- 无权限提示 -->
        <template v-else>
          <el-empty description="您暂无查看业绩的权限">
            <el-button type="primary" @click="close">返回</el-button>
          </el-empty>
        </template>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">关闭</el-button>
        <el-button type="primary" @click="exportData" :loading="exportLoading">
          导出数据
        </el-button>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { PerformanceApi, type PerformanceDataVO, type PerformancePageReqVO } from '@/api/erp/performance'
import SupervisorPerformanceView from './components/SupervisorPerformanceView.vue'
import TeamLeaderPerformanceView from './components/TeamLeaderPerformanceView.vue'
import PersonalPerformanceView from './components/PersonalPerformanceView.vue'

defineOptions({ name: 'PerformanceDialog' })

// 用户角色类型
type UserRole = 'SUPER_ADMIN' | 'SUPERVISOR' | 'TEAM_LEADER' | 'TEAM_MEMBER' | 'NONE'

// 业绩数据类型（复用API类型）
type PerformanceData = PerformanceDataVO

const dialogVisible = ref(false)
const loading = ref(false)
const exportLoading = ref(false)

// 表单引用
const filterFormRef = ref()

// 用户store
const userStore = useUserStore()

// 筛选参数
const filterParams = reactive({
  dateRange: [] as string[], // 日期范围 ['2024-01-01', '2024-01-31']
  productIds: [] as number[], // 产品ID列表
  dimension: 'monthly' as 'all' | 'daily' | 'weekly' | 'monthly' // 数据维度
})

// 产品列表
const productList = ref([
  { id: 1, name: '游戏1' },
  { id: 2, name: '游戏2' },
  { id: 3, name: '游戏3' },
  { id: 4, name: '游戏4' }
])

// 当前用户信息
const currentUser = ref({
  userId: '',
  userName: '',
  departmentId: '',
  departmentName: ''
})

// 用户角色
const userRole = ref<UserRole>('NONE')

// 业绩数据
const performanceData = ref<PerformanceData>({})

/** 获取角色标签类型 */
const getRoleTagType = (role: UserRole) => {
  switch (role) {
    case 'SUPER_ADMIN': return 'danger'
    case 'SUPERVISOR': return 'danger'
    case 'TEAM_LEADER': return 'warning'
    case 'TEAM_MEMBER': return 'success'
    default: return 'info'
  }
}

/** 获取角色文本 */
const getRoleText = (role: UserRole) => {
  switch (role) {
    case 'SUPER_ADMIN': return '超级管理员'
    case 'SUPERVISOR': return '部门主管'
    case 'TEAM_LEADER': return '组长'
    case 'TEAM_MEMBER': return '组员'
    default: return '未知角色'
  }
}

/** 获取角色描述 */
const getRoleDescription = (role: UserRole) => {
  switch (role) {
    case 'SUPER_ADMIN': 
      return '您拥有最高权限，可以查看全公司所有产品、渠道、区服的业绩数据'
    case 'SUPERVISOR': 
      return '您可以查看基于资源绑定权限的业绩数据（运维：产品+渠道+区服，运营：产品+渠道）'
    case 'TEAM_LEADER': 
      return '您可以查看本组成员基于资源绑定权限的业绩数据汇总'
    case 'TEAM_MEMBER': 
      return '您只能查看基于个人资源绑定权限的业绩数据（运维：产品+渠道+区服，运营：产品+渠道）'
    default: 
      return '系统未能识别您的权限级别，请联系管理员配置资源绑定权限。注意：业绩基于订单的产品、渠道、区服与您的资源绑定权限匹配计算'
  }
}

/** 初始化筛选参数 */
const initFilterParams = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  
  // 设置默认为当月第一天到最后一天
  const firstDay = `${year}-${month}-01`
  const lastDay = new Date(year, now.getMonth() + 1, 0).getDate()
  const lastDayStr = `${year}-${month}-${String(lastDay).padStart(2, '0')}`
  
  filterParams.dateRange = [firstDay, lastDayStr]
  filterParams.productIds = []
  filterParams.dimension = 'monthly'
}

/** 打开对话框 */
const open = async () => {
  dialogVisible.value = true
  initFilterParams()
  await loadUserInfo()
  await loadProductList()
  await loadPerformanceData()
}

/** 加载产品列表 */
const loadProductList = async () => {
  try {
    // TODO: 调用API获取产品列表
    // const products = await ProductApi.getProductList()
    // productList.value = products
    
    // 模拟数据
    productList.value = [
      { id: 1, name: '传奇世界' },
      { id: 2, name: '魔域手游' },
      { id: 3, name: '仙境传说' },
      { id: 4, name: '神武4' },
      { id: 5, name: '大话西游' }
    ]
  } catch (error) {
    console.error('加载产品列表失败:', error)
  }
}

/** 关闭对话框 */
const close = () => {
  dialogVisible.value = false
  resetData()
}

/** 重置数据 */
const resetData = () => {
  performanceData.value = {}
  userRole.value = 'NONE'
  currentUser.value = {
    userId: '',
    userName: '',
    departmentId: '',
    departmentName: ''
  }
}

/** 加载用户信息 */
const loadUserInfo = async () => {
  try {
    loading.value = true
    
    // 从userStore获取用户信息
    const user = userStore.getUser
    if (!user || !user.id) {
      ElMessage.error('获取用户信息失败')
      return
    }

    // 设置用户信息
    currentUser.value = {
      userId: user.id.toString(),
      userName: user.nickname || '当前用户',
      departmentId: user.deptId?.toString() || '',
      departmentName: '用户部门' // 实际应该从部门API获取
    }
    
    // 调用API获取用户角色
    const role = await PerformanceApi.getUserRole()
    userRole.value = role as UserRole
    
  } catch (error) {
    console.error('加载用户信息失败:', error)
    ElMessage.error('加载用户信息失败')
  } finally {
    loading.value = false
  }
}

/** 加载业绩数据 */
const loadPerformanceData = async () => {
  try {
    loading.value = true
    
    // 构建查询参数
    const queryParams: PerformancePageReqVO = {
      startDate: filterParams.dateRange[0],
      endDate: filterParams.dateRange[1],
      productIds: filterParams.productIds.length > 0 ? filterParams.productIds : undefined
    }
    
    console.log('查询参数:', queryParams)
    
    // 使用统一的业绩数据接口
    // 后端会根据当前用户的角色和资源绑定权限，通过SQL-based过滤自动返回相应的业绩数据
    // 不再需要前端区分不同的接口，所有权限控制在后端统一处理
    performanceData.value = await PerformanceApi.getPerformanceData(queryParams)
    
  } catch (error) {
    console.error('加载业绩数据失败:', error)
    ElMessage.error('加载业绩数据失败')
  } finally {
    loading.value = false
  }
}

/** 处理日期范围变化 */
const handleDateRangeChange = (dateRange: string[]) => {
  console.log('日期范围变化:', dateRange)
  // 将月份范围转换为日期范围
  if (dateRange && dateRange.length === 2) {
    const startMonth = dateRange[0] // YYYY-MM 格式
    const endMonth = dateRange[1]   // YYYY-MM 格式
    
    // 开始日期为月份第一天
    const startDate = `${startMonth}-01`
    
    // 结束日期为月份最后一天
    const [endYear, endMonthNum] = endMonth.split('-').map(Number)
    const lastDay = new Date(endYear, endMonthNum, 0).getDate()
    const endDate = `${endMonth}-${String(lastDay).padStart(2, '0')}`
    
    filterParams.dateRange = [startDate, endDate]
    loadPerformanceData()
  }
}

/** 处理产品变化 */
const handleProductChange = (productIds: number[]) => {
  console.log('产品筛选变化:', productIds)
  // 自动查询
  loadPerformanceData()
}

/** 处理数据维度变化 */
const handleDimensionChange = (dimension: string) => {
  console.log('数据维度变化:', dimension)
  // 自动查询
  loadPerformanceData()
}

/** 手动搜索 */
const handleSearch = () => {
  loadPerformanceData()
}

/** 重置筛选条件 */
const resetFilter = () => {
  initFilterParams()
  loadPerformanceData()
}

/** 处理日期变更 */
const handleDateChange = (date: string) => {
  // 兼容旧的日期变更方法，现在使用日期范围
  if (date) {
    filterParams.dateRange = [date, date]
    loadPerformanceData()
  }
}

/** 处理展开事件 */
const handleExpand = (type: string, id: string) => {
  console.log('展开:', type, id)
  // TODO: 实现展开逻辑
}

/** 导出数据 */
const exportData = async () => {
  try {
    exportLoading.value = true
    // TODO: 实现导出功能
    // await PerformanceApi.exportData(selectedDate.value, userRole.value)
    ElMessage.success('导出功能开发中...')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  open,
  close
})
</script>

<style scoped>
.performance-container {
  max-height: 700px;
  overflow-y: auto;
}

.user-info-card {
  margin-bottom: 20px;
}

.user-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.user-details {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-details h3 {
  margin: 0;
  font-size: 18px;
  color: var(--el-text-color-primary);
}

.date-selector {
  min-width: 200px;
}

.role-description {
  margin: 0;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  line-height: 1.5;
}

.performance-content {
  min-height: 400px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>