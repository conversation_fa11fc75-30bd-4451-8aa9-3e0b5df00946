<template>
  <div class="department-container">
    <!-- 搜索工作栏 -->
    <ContentWrap>
      <el-form
        class="-mb-15px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="68px"
      >
        <el-form-item label="部门名称" prop="deptName">
          <el-input
            v-model="queryParams.deptName"
            placeholder="请输入部门名称"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="部门编码" prop="deptCode">
          <el-input
            v-model="queryParams.deptCode"
            placeholder="请输入部门编码"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="部门层级" prop="deptLevel">
          <el-select
            v-model="queryParams.deptLevel"
            placeholder="请选择部门层级"
            clearable
            class="!w-240px"
          >
            <el-option label="主管部门" :value="1" />
            <el-option label="小组" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            class="!w-240px"
          >
            <el-option label="正常" :value="1" />
            <el-option label="停用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
          <el-button
            type="primary"
            plain
            @click="openForm('create')"
            v-hasPermi="['erp:department:create']"
          >
            <Icon icon="ep:plus" class="mr-5px" /> 新增
          </el-button>
          <el-button type="success" plain @click="toggleExpandAll">
            <Icon icon="ep:sort" class="mr-5px" /> 展开/折叠
          </el-button>
          <el-button type="warning" plain @click="openPerformanceView">
            <Icon icon="ep:data-analysis" class="mr-5px" /> 业绩查看
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>

    <!-- 部门树形列表 -->
    <ContentWrap>
      <el-table
        v-loading="loading"
        :data="departmentList"
        :stripe="true"
        :show-overflow-tooltip="true"
        v-if="refreshTable"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="isExpandAll"
        border
      >
        <el-table-column label="部门名称" align="left" prop="deptName" width="200px">
          <template #default="scope">
            <span>{{ scope.row.deptName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="部门编码" align="center" prop="deptCode" width="150px" />
        <el-table-column label="部门层级" align="center" prop="deptLevel" width="100px">
          <template #default="scope">
            <el-tag :type="getDeptLevelTagType(scope.row.deptLevel)" effect="plain">
              {{ getDeptLevelText(scope.row.deptLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="排序" align="center" prop="sortOrder" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <!-- <el-table-column label="描述" align="center" prop="description" /> -->
        <el-table-column label="创建时间" align="center" prop="createTime">
          <template #default="scope">
            {{ formatTime(scope.row.createTime / 1000) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button
              link
              type="primary"
              @click="openForm('update', scope.row.id)"
              v-hasPermi="['erp:department:update']"
            >
              修改
            </el-button>
            <el-button
              link
              type="primary"
              @click="openForm('create', undefined, scope.row.id)"
              v-hasPermi="['erp:department:create']"
            >
              新增
            </el-button>
            <el-button link type="primary" @click="handleUserManage(scope.row)">
              用户管理
            </el-button>
            <el-button
              link
              type="danger"
              @click="handleDelete(scope.row.id)"
              v-hasPermi="['erp:department:delete']"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </ContentWrap>

    <!-- 部门表单弹窗 -->
    <DepartmentForm ref="formRef" @success="getList" />

    <!-- 用户管理弹窗 -->
    <UserManageDialog ref="userManageRef" @success="getList" />

    <!-- 业绩查看对话框 -->
    <PerformanceDialog ref="performanceDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { DepartmentApi, type DepartmentVO, type DepartmentPageReqVO } from '@/api/erp/department'
import DepartmentForm from './DepartmentForm.vue'
import UserManageDialog from './UserManageDialog.vue'
import PerformanceDialog from './PerformanceDialog.vue'
import { formatTime } from '@/utils/formatTime'

defineOptions({ name: 'ErpDepartment' })

const loading = ref(true)
const departmentList = ref<DepartmentVO[]>([])
const refreshTable = ref(true)
const isExpandAll = ref(false)

// 查询参数
const queryParams = reactive<DepartmentPageReqVO>({
  pageNo: 1,
  pageSize: 100,
  deptName: undefined,
  deptCode: undefined,
  deptLevel: undefined,
  status: undefined
})

const queryFormRef = ref()
const formRef = ref()
const userManageRef = ref()

/** 查询部门列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DepartmentApi.getDepartmentTree()
    departmentList.value = data || []
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 展开/折叠操作 */
const toggleExpandAll = () => {
  refreshTable.value = false
  isExpandAll.value = !isExpandAll.value
  // 重新渲染表格
  setTimeout(() => {
    refreshTable.value = true
  }, 10)
}

/** 新增/修改操作 */
const openForm = (type: string, id?: number, parentId?: number) => {
  formRef.value.open(type, id, parentId)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    await ElMessageBox.confirm('是否确认删除此部门?', '系统提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await DepartmentApi.deleteDepartment(id)
    ElMessage.success('删除成功')
    await getList()
  } catch {}
}

/** 状态修改 */
const handleStatusChange = async (row: DepartmentVO) => {
  try {
    await DepartmentApi.updateDepartment({
      id: row.id,
      status: row.status
    })
    ElMessage.success('状态修改成功')
  } catch {
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1
  }
}

/** 用户管理 */
const handleUserManage = (row: DepartmentVO) => {
  userManageRef.value.open(row)
}

/** 获取部门层级标签类型 */
const getDeptLevelTagType = (level: number) => {
  switch (level) {
    case 1:
      return 'danger'
    case 2:
      return 'warning'
    default:
      return 'info'
  }
}

/** 获取部门层级文本 */
const getDeptLevelText = (level: number) => {
  switch (level) {
    case 1:
      return '主管部门'
    case 2:
      return '小组'
    default:
      return '未知'
  }
}

/** 格式化日期 */
const formatDate = (date: string | number) => {
  if (!date) return ''
  return new Date(typeof date === 'number' ? date * 1000 : date).toLocaleString()
}

/** 业绩查看对话框 */
const performanceDialogRef = ref()

/** 打开业绩查看 */
const openPerformanceView = () => {
  performanceDialogRef.value?.open()
}

/** 初始化 */
onMounted(() => {
  getList()
})
</script>

<style scoped>
.department-container {
  padding: 20px;
}

.el-table {
  border-radius: 8px;
}

.el-table .el-table__header-wrapper {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.el-table .el-table__body-wrapper {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}
</style>
