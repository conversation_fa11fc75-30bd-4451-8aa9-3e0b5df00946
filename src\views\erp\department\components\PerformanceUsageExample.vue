<template>
  <div class="performance-usage-example">
    <h3>业绩查询使用说明</h3>
    <el-alert type="info" show-icon :closable="false">
      <template #title>
        <strong>前后端对接说明</strong>
      </template>
      <div>
        <p><strong>后端修改：</strong></p>
        <ul>
          <li>后端已统一采用 SQL-based 权限过滤模式</li>
          <li>所有业绩查询都通过 <code>dataPermissionService.generatePermissionSql()</code> 生成权限SQL</li>
          <li>权限逻辑：运维人员基于产品+渠道+区服，运营人员基于产品+渠道</li>
          <li>业绩计算基于订单数据与资源绑定权限的匹配，而不是用户ID匹配</li>
        </ul>
        
        <p><strong>前端调用：</strong></p>
        <ul>
          <li>使用统一的 <code>PerformanceApi.getPerformanceData()</code> 接口</li>
          <li>后端会根据当前用户角色和权限自动返回相应数据</li>
          <li>不再需要前端区分多个不同的接口</li>
        </ul>
        
        <p><strong>权限说明：</strong></p>
        <ul>
          <li><strong>运维人员：</strong>业绩基于绑定的产品+渠道+区服组合的订单金额计算</li>
          <li><strong>运营人员：</strong>业绩基于绑定的产品+渠道组合的订单金额计算</li>
          <li><strong>主管/组长：</strong>可查看下属人员权限范围内的业绩汇总</li>
        </ul>
      </div>
    </el-alert>
    
    <div class="code-example">
      <h4>正确的前端调用示例：</h4>
      <pre><code>
// 查询参数
const queryParams: PerformancePageReqVO = {
  startDate: '2024-01-01',
  endDate: '2024-01-31',
  productIds: [1, 2, 3] // 可选
}

// 统一调用接口
const performanceData = await PerformanceApi.getPerformanceData(queryParams)

// 后端会自动：
// 1. 识别当前用户角色
// 2. 获取用户的资源绑定权限
// 3. 生成权限SQL过滤条件
// 4. 在数据库层面进行权限过滤和业绩聚合
// 5. 返回符合权限的业绩数据
      </code></pre>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({ name: 'PerformanceUsageExample' })
</script>

<style scoped>
.performance-usage-example {
  padding: 20px;
}

.code-example {
  margin-top: 20px;
}

.code-example pre {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
}

.code-example code {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
}

ul {
  padding-left: 20px;
}

li {
  margin-bottom: 5px;
}
</style>