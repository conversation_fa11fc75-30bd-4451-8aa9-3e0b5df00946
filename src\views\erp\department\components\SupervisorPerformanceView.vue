<template>
  <div class="supervisor-performance">
    <!-- 部门总览 -->
    <el-card shadow="hover" class="overview-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <Icon icon="ep:data-board" class="header-icon" />
            <span class="card-title">部门业绩总览</span>
          </div>
          <div class="header-right">
            <el-tag type="primary" size="large">
              {{ formatDateRange(props.filterParams.dateRange) }}
            </el-tag>
          </div>
        </div>
      </template>

      <div class="overview-content">
        <div class="overview-stats">
          <div class="stat-item primary">
            <div class="stat-icon">
              <Icon icon="ep:money" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{
                formatCurrency(performanceData.departmentTotal || 0)
              }}</div>
              <div class="stat-label">部门总业绩</div>
            </div>
          </div>

          <div class="stat-item success">
            <div class="stat-icon">
              <Icon icon="ep:box" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ getProductCount() }}</div>
              <div class="stat-label">产品数量</div>
            </div>
          </div>

          <div class="stat-item warning">
            <div class="stat-icon">
              <Icon icon="ep:user-filled" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ getTotalMemberCount() }}</div>
              <div class="stat-label">总人数</div>
            </div>
          </div>

          <div class="stat-item info">
            <div class="stat-icon">
              <Icon icon="ep:trophy" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ formatCurrency(getAveragePerformance()) }}</div>
              <div class="stat-label">人均业绩</div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 按产品分组的业绩展示 -->
    <div v-if="performanceData.productBreakdown && performanceData.productBreakdown.length > 0">
      <el-card
        v-for="product in performanceData.productBreakdown"
        :key="product.productId"
        shadow="hover"
        class="product-card"
      >
        <template #header>
          <div class="card-header">
            <div class="header-left">
              <Icon icon="ep:goods" class="header-icon product-icon" />
              <span class="card-title">{{ product.productName }}</span>
              <el-tag type="success" size="small" class="product-amount">
                {{ formatCurrency(product.totalAmount) }}
              </el-tag>
            </div>
            <div class="header-right">
              <el-button size="small" @click="toggleProductExpansion(product.productId)">
                {{ expandedProducts.includes(product.productId) ? '收起' : '展开' }}
              </el-button>
            </div>
          </div>
        </template>

        <div class="product-content">
          <!-- 产品业绩概览 -->
          <div class="product-overview">
            <div class="product-stats">
              <div class="product-stat">
                <span class="stat-label">产品业绩</span>
                <span class="stat-value">{{ formatCurrency(product.totalAmount) }}</span>
              </div>
              <div class="product-stat">
                <span class="stat-label">团队数量</span>
                <span class="stat-value">{{ product.teamData?.length || 0 }}个</span>
              </div>
              <div class="product-stat">
                <span class="stat-label">参与人数</span>
                <span class="stat-value">{{ getProductMemberCount(product) }}人</span>
              </div>
              <div class="product-stat">
                <span class="stat-label">占部门比例</span>
                <span class="stat-value">{{ getProductPercentage(product.totalAmount) }}%</span>
              </div>
            </div>
          </div>

          <!-- 团队详情 -->
          <div v-show="expandedProducts.includes(product.productId)" class="teams-detail">
            <el-table
              :data="product.teamData || []"
              border
              row-key="teamId"
              :expand-row-keys="expandedTeams"
              @expand-change="handleTeamExpand"
            >
              <el-table-column type="expand">
                <template #default="{ row }">
                  <div class="team-members-detail">
                    <h4>{{ row.teamName }} - 成员详情</h4>
                    <el-table :data="row.members || []" size="small" border>
                      <el-table-column label="排名" width="80" align="center">
                        <template #default="{ row: member }">
                          <el-tag :type="getRankTagType(member.rank)" size="small">
                            第{{ member.rank }}名
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="用户名" prop="userName" width="120" />
                      <el-table-column label="业绩金额" align="right" width="150">
                        <template #default="{ row: member }">
                          <span class="amount-text">{{ formatCurrency(member.amount) }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="占组比例" align="right" width="120">
                        <template #default="{ row: member }">
                          <span class="percentage-text">
                            {{ getTeamMemberPercentage(member.amount, row.amount) }}%
                          </span>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="排名" width="80" align="center">
                <template #default="{ $index }">
                  <div class="team-rank">
                    <el-tag :type="getTeamRankTagType($index + 1)" size="large" effect="dark">
                      {{ $index + 1 }}
                    </el-tag>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="团队名称" min-width="150">
                <template #default="{ row }">
                  <div class="team-info">
                    <Icon icon="ep:user-filled" class="team-icon" />
                    <span class="team-name">{{ row.teamName }}</span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="团队业绩" align="right" width="180">
                <template #default="{ row }">
                  <div class="team-performance">
                    <span class="amount-large">{{ formatCurrency(row.amount) }}</span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="人数" align="center" width="80">
                <template #default="{ row }">
                  <el-tag type="info" size="small">{{ row.memberCount }}人</el-tag>
                </template>
              </el-table-column>

              <el-table-column label="人均业绩" align="right" width="150">
                <template #default="{ row }">
                  <span class="average-amount">
                    {{ formatCurrency(Math.round(row.amount / row.memberCount)) }}
                  </span>
                </template>
              </el-table-column>

              <el-table-column label="占产品比例" align="right" width="120">
                <template #default="{ row }">
                  <div class="product-percentage">
                    <el-progress
                      :percentage="getTeamProductPercentage(row.amount, product.totalAmount)"
                      :show-text="false"
                      :stroke-width="8"
                      :color="
                        getProgressColor(getTeamProductPercentage(row.amount, product.totalAmount))
                      "
                    />
                    <span class="percentage-value">
                      {{ getTeamProductPercentage(row.amount, product.totalAmount) }}%
                    </span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 产品业绩对比图表 -->
    <el-card shadow="hover" class="charts-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <Icon icon="ep:trend-charts" class="header-icon" />
            <span class="card-title">产品业绩对比</span>
          </div>
          <div class="header-right">
            <el-radio-group v-model="chartType" size="small">
              <el-radio-button label="pie">饼图</el-radio-button>
              <el-radio-button label="bar">柱状图</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>

      <div class="charts-content">
        <div ref="chartContainer" class="chart-container"></div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

interface Props {
  performanceData: any
  filterParams: {
    dateRange: string[]
    productIds: number[]
    dimension: string
  }
}

interface Emits {
  (e: 'expand', type: string, id: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const loading = ref(false)
const expandedTeams = ref<string[]>([])
const expandedProducts = ref<number[]>([])
const chartType = ref<'pie' | 'bar'>('pie')
const chartContainer = ref()
let chartInstance: echarts.ECharts | null = null

/** 格式化金额 */
const formatCurrency = (amount: number) => {
  return amount.toLocaleString()
}

/** 格式化日期范围 */
const formatDateRange = (dateRange: string[]) => {
  if (!dateRange || dateRange.length === 0) return '未选择日期'
  if (dateRange.length === 1 || dateRange[0] === dateRange[1]) {
    return dateRange[0]
  }
  return `${dateRange[0]} 至 ${dateRange[1]}`
}

/** 获取产品数量 */
const getProductCount = () => {
  return props.performanceData.productBreakdown?.length || 0
}

/** 获取总人数 */
const getTotalMemberCount = () => {
  if (!props.performanceData.productBreakdown) return 0
  return props.performanceData.productBreakdown.reduce((total, product) => {
    return (
      total + (product.teamData?.reduce((teamTotal, team) => teamTotal + team.memberCount, 0) || 0)
    )
  }, 0)
}

/** 获取平均业绩 */
const getAveragePerformance = () => {
  const totalMembers = getTotalMemberCount()
  if (totalMembers === 0) return 0
  return Math.round((props.performanceData.departmentTotal || 0) / totalMembers)
}

/** 获取产品成员数量 */
const getProductMemberCount = (product: any) => {
  return product.teamData?.reduce((total: number, team: any) => total + team.memberCount, 0) || 0
}

/** 获取产品占部门比例 */
const getProductPercentage = (productAmount: number) => {
  if (!props.performanceData.departmentTotal) return 0
  return Math.round((productAmount / props.performanceData.departmentTotal) * 100)
}

/** 获取团队在产品中的占比 */
const getTeamProductPercentage = (teamAmount: number, productTotal: number) => {
  if (productTotal === 0) return 0
  return Math.round((teamAmount / productTotal) * 100)
}

/** 获取团队成员占比 */
const getTeamMemberPercentage = (memberAmount: number, teamAmount: number) => {
  if (teamAmount === 0) return 0
  return ((memberAmount / teamAmount) * 100).toFixed(1)
}

/** 切换产品展开状态 */
const toggleProductExpansion = (productId: number) => {
  const index = expandedProducts.value.indexOf(productId)
  if (index > -1) {
    expandedProducts.value.splice(index, 1)
  } else {
    expandedProducts.value.push(productId)
  }
}

/** 获取排名标签类型 */
const getRankTagType = (rank: number) => {
  if (rank === 1) return 'danger'
  if (rank === 2) return 'warning'
  if (rank === 3) return 'success'
  return 'info'
}

/** 获取团队排名标签类型 */
const getTeamRankTagType = (rank: number) => {
  if (rank === 1) return 'danger'
  if (rank === 2) return 'warning'
  if (rank === 3) return 'success'
  return 'info'
}

/** 获取进度条颜色 */
const getProgressColor = (percentage: number) => {
  if (percentage >= 40) return '#f56c6c'
  if (percentage >= 25) return '#e6a23c'
  return '#67c23a'
}

/** 处理团队展开 */
const handleTeamExpand = (row: any, expandedRows: any[]) => {
  if (expandedRows.includes(row)) {
    expandedTeams.value.push(row.teamId)
  } else {
    expandedTeams.value = expandedTeams.value.filter((id) => id !== row.teamId)
  }
  emit('expand', 'team', row.teamId)
}

/** 初始化图表 */
const initChart = () => {
  if (!chartContainer.value) return

  if (chartInstance) {
    chartInstance.dispose()
  }

  chartInstance = echarts.init(chartContainer.value)
  updateChart()
}

/** 更新图表 */
const updateChart = () => {
  if (!chartInstance || !props.performanceData.productBreakdown) return

  if (chartType.value === 'pie') {
    // 产品业绩分布饼图
    const option = {
      title: {
        text: '产品业绩分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: props.performanceData.productBreakdown.map((product) => product.productName)
      },
      series: [
        {
          name: '产品业绩',
          type: 'pie',
          radius: '50%',
          data: props.performanceData.productBreakdown.map((product) => ({
            value: product.totalAmount,
            name: product.productName
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    chartInstance.setOption(option)
  } else {
    // 产品业绩对比柱状图
    const option = {
      title: {
        text: '产品业绩对比',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: props.performanceData.productBreakdown.map((product) => product.productName),
        axisTick: {
          alignWithLabel: true
        }
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '业绩金额',
          type: 'bar',
          barWidth: '60%',
          data: props.performanceData.productBreakdown.map((product) => product.totalAmount),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#83bff6' },
              { offset: 0.5, color: '#188df0' },
              { offset: 1, color: '#188df0' }
            ])
          }
        }
      ]
    }
    chartInstance.setOption(option)
  }
}

// 监听图表类型变化
watch(chartType, () => {
  updateChart()
})

// 监听数据和筛选参数变化
watch(
  [() => props.performanceData, () => props.filterParams],
  () => {
    nextTick(() => {
      updateChart()
    })
  },
  { deep: true }
)

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style scoped>
.supervisor-performance {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  font-size: 18px;
  color: var(--el-color-primary);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 总览卡片样式 */
.overview-content {
  padding: 10px 0;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid;
  background: var(--el-bg-color-page);
}

.stat-item.primary {
  border-left-color: var(--el-color-primary);
}

.stat-item.success {
  border-left-color: var(--el-color-success);
}

.stat-item.warning {
  border-left-color: var(--el-color-warning);
}

.stat-icon {
  font-size: 32px;
  margin-right: 15px;
  color: var(--el-color-primary);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

/* 团队表格样式 */
.teams-content {
  margin-top: 15px;
}

.team-rank {
  display: flex;
  justify-content: center;
}

.team-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.team-icon {
  color: var(--el-color-primary);
}

.team-name {
  font-weight: 500;
}

.team-performance .amount-large {
  font-size: 18px;
  font-weight: bold;
  color: var(--el-color-primary);
}

.average-amount {
  color: var(--el-text-color-regular);
  font-weight: 500;
}

.department-percentage {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.percentage-value {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

/* 产品卡片样式 */
.product-card {
  margin-bottom: 20px;
}

.product-icon {
  color: var(--el-color-success);
}

.product-amount {
  margin-left: 10px;
}

.product-content {
  margin-top: 15px;
}

.product-overview {
  background: var(--el-bg-color-page);
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.product-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.product-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.product-stat .stat-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}

.product-stat .stat-value {
  font-size: 18px;
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.teams-detail {
  margin-top: 15px;
}

.product-percentage {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}
.team-members-detail {
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  margin: 10px;
}

.team-members-detail h4 {
  margin: 0 0 15px 0;
  color: var(--el-text-color-primary);
}

.amount-text {
  font-weight: 600;
  color: var(--el-color-primary);
}

.percentage-text {
  color: var(--el-text-color-regular);
}

.trend-up {
  color: var(--el-color-success);
}

.trend-down {
  color: var(--el-color-danger);
}

.trend-neutral {
  color: var(--el-text-color-placeholder);
}

/* 图表样式 */
.charts-content {
  margin-top: 15px;
}

.chart-container {
  width: 100%;
  height: 400px;
}
</style>
