<template>
  <div class="hierarchy-toolbar">
    <div class="toolbar-content">
      <div class="toolbar-left">
        <!-- 展开/收起控制 -->
        <div class="control-group expand-controls">
          <div class="control-label">
            <Icon icon="ep:operation" />
            <span>展开控制</span>
          </div>
          <div class="control-buttons">
            <button 
              class="control-btn primary" 
              @click="expandAll" 
              :disabled="loading"
            >
              <Icon icon="ep:plus" />
              <span>全部展开</span>
            </button>
            <button 
              class="control-btn secondary" 
              @click="collapseAll" 
              :disabled="loading"
            >
              <Icon icon="ep:minus" />
              <span>全部收起</span>
            </button>
          </div>
        </div>

        <!-- 排序控制 -->
        <div class="control-group sort-controls">
          <div class="control-label">
            <Icon icon="ep:sort" />
            <span>排序方式</span>
          </div>
          <div class="control-buttons">
            <select
              v-model="currentSort"
              @change="handleSortChange"
              class="sort-select"
            >
              <option value="amount">按业绩排序</option>
              <option value="rank">按排名排序</option>
              <option value="name">按名称排序</option>
              <option value="memberCount">按人数排序</option>
            </select>
            
            <button
              class="control-btn icon-btn"
              @click="toggleSortOrder"
              :title="sortOrder === 'desc' ? '降序' : '升序'"
            >
              <Icon :icon="sortOrder === 'desc' ? 'ep:arrow-down' : 'ep:arrow-up'" />
            </button>
          </div>
        </div>

        <!-- 筛选控制 -->
        <div class="control-group filter-controls">
          <div class="control-label">
            <Icon icon="ep:filter" />
            <span>筛选条件</span>
          </div>
          <div class="control-buttons">
            <select
              v-model="nodeTypeFilter"
              @change="handleFilterChange"
              class="filter-select"
            >
              <option value="">全部类型</option>
              <option value="DEPARTMENT">部门</option>
              <option value="GROUP">小组</option>
              <option value="PERSON">个人</option>
            </select>
          </div>
        </div>
      </div>

      <div class="toolbar-right">
        <!-- 显示模式切换 -->
        <div class="control-group view-mode-controls">
          <div class="control-label">
            <Icon icon="ep:view" />
            <span>显示模式</span>
          </div>
          <div class="view-mode-buttons">
            <button
              v-for="mode in viewModes"
              :key="mode.value"
              :class="['view-mode-btn', { active: viewMode === mode.value }]"
              @click="handleViewModeChange(mode.value)"
              :title="mode.label"
            >
              <Icon :icon="mode.icon" />
              <span>{{ mode.label }}</span>
            </button>
          </div>
        </div>

        <!-- 搜索框 -->
        <div class="control-group search-controls">
          <div class="control-label">
            <Icon icon="ep:search" />
            <span>搜索</span>
          </div>
          <div class="search-input-wrapper">
            <input
              v-model="searchKeyword"
              type="text"
              placeholder="搜索节点名称..."
              class="search-input"
              @input="handleSearch"
            />
            <button 
              v-if="searchKeyword" 
              class="clear-search-btn"
              @click="clearSearch"
            >
              <Icon icon="ep:close" />
            </button>
          </div>
        </div>

        <!-- 刷新按钮 -->
        <div class="control-group refresh-controls">
          <button
            class="refresh-btn"
            @click="handleRefresh"
            :disabled="loading"
            title="刷新数据"
          >
            <Icon icon="ep:refresh" :class="{ 'spinning': loading }" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

interface Props {
  loading?: boolean
}

interface Emits {
  (e: 'expand-all'): void
  (e: 'collapse-all'): void
  (e: 'sort-change', sortBy: string, order: 'asc' | 'desc'): void
  (e: 'filter-change', filters: { nodeType?: string, keyword?: string }): void
  (e: 'view-mode-change', mode: string): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 工具栏状态
const currentSort = ref('amount')
const sortOrder = ref<'asc' | 'desc'>('desc')
const nodeTypeFilter = ref('')
const searchKeyword = ref('')
const viewMode = ref('tree')

// 显示模式选项
const viewModes = [
  { value: 'tree', label: '树形视图', icon: 'ep:operation' },
  { value: 'compact', label: '紧凑视图', icon: 'ep:list' },
  { value: 'card', label: '卡片视图', icon: 'ep:grid' }
]

/** 展开所有节点 */
const expandAll = () => {
  emit('expand-all')
}

/** 收起所有节点 */
const collapseAll = () => {
  emit('collapse-all')
}

/** 切换排序顺序 */
const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'desc' ? 'asc' : 'desc'
  handleSortChange()
}

/** 处理排序变化 */
const handleSortChange = () => {
  emit('sort-change', currentSort.value, sortOrder.value)
}

/** 处理筛选变化 */
const handleFilterChange = () => {
  emit('filter-change', {
    nodeType: nodeTypeFilter.value || undefined,
    keyword: searchKeyword.value || undefined
  })
}

/** 处理搜索 */
const handleSearch = () => {
  // 防抖处理
  setTimeout(() => {
    handleFilterChange()
  }, 300)
}

/** 清除搜索 */
const clearSearch = () => {
  searchKeyword.value = ''
  handleFilterChange()
}

/** 处理显示模式变化 */
const handleViewModeChange = (mode: string) => {
  viewMode.value = mode
  emit('view-mode-change', mode)
}

/** 处理刷新 */
const handleRefresh = () => {
  emit('refresh')
}

// 监听搜索关键词变化
watch(searchKeyword, () => {
  handleSearch()
})
</script>

<style scoped>
.hierarchy-toolbar {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.95) 0%, 
    rgba(248, 250, 252, 0.9) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 20px;
  margin-bottom: 24px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(255, 255, 255, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.hierarchy-toolbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(99, 102, 241, 0.3) 25%,
    rgba(168, 85, 247, 0.3) 50%,
    rgba(236, 72, 153, 0.3) 75%,
    transparent 100%);
}

.toolbar-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px 32px;
  gap: 32px;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: flex-start;
  gap: 24px;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 0;
}

.control-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.control-label .el-icon {
  font-size: 14px;
  color: #6366f1;
}

.control-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 按钮样式 */
.control-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 10px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.8);
  color: #374151;
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.control-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-btn.primary {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  border: none;
}

.control-btn.primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #5b5bf6 0%, #7c3aed 100%);
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.3);
}

.control-btn.secondary {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
  border: 1px solid rgba(107, 114, 128, 0.2);
}

.control-btn.secondary:hover:not(:disabled) {
  background: rgba(107, 114, 128, 0.15);
  color: #4b5563;
}

.control-btn.icon-btn {
  padding: 8px;
  min-width: 36px;
  justify-content: center;
}

/* 选择框样式 */
.sort-select,
.filter-select {
  padding: 8px 12px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.9);
  color: #374151;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
}

.sort-select:hover,
.filter-select:hover {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(99, 102, 241, 0.3);
}

.sort-select:focus,
.filter-select:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 显示模式按钮 */
.view-mode-buttons {
  display: flex;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  padding: 4px;
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.view-mode-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background: transparent;
  color: #6b7280;
  white-space: nowrap;
}

.view-mode-btn:hover {
  background: rgba(255, 255, 255, 0.8);
  color: #374151;
}

.view-mode-btn.active {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

/* 搜索框样式 */
.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 200px;
  padding: 8px 12px;
  padding-right: 36px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.9);
  color: #374151;
  font-size: 13px;
  transition: all 0.2s ease;
}

.search-input::placeholder {
  color: #9ca3af;
}

.search-input:hover {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(99, 102, 241, 0.3);
}

.search-input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.clear-search-btn {
  position: absolute;
  right: 8px;
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 50%;
  background: rgba(107, 114, 128, 0.2);
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.2s ease;
}

.clear-search-btn:hover {
  background: rgba(107, 114, 128, 0.3);
  color: #4b5563;
}

/* 刷新按钮 */
.refresh-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.08) 100%);
  color: #10b981;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: all 0.2s ease;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.refresh-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(5, 150, 105, 0.12) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .toolbar-content {
    gap: 24px;
  }
  
  .toolbar-left,
  .toolbar-right {
    gap: 20px;
  }
}

@media (max-width: 1200px) {
  .toolbar-content {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }
  
  .toolbar-left,
  .toolbar-right {
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 16px;
  }
  
  .control-group {
    flex: 1;
    min-width: 200px;
  }
}

@media (max-width: 768px) {
  .hierarchy-toolbar {
    margin-bottom: 16px;
  }
  
  .toolbar-content {
    padding: 20px 24px;
    gap: 16px;
  }
  
  .toolbar-left,
  .toolbar-right {
    flex-direction: column;
    gap: 12px;
  }
  
  .control-group {
    min-width: 0;
  }
  
  .control-buttons {
    flex-wrap: wrap;
  }
  
  .view-mode-buttons {
    width: 100%;
  }
  
  .view-mode-btn {
    flex: 1;
    justify-content: center;
  }
  
  .search-input {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .toolbar-content {
    padding: 16px 20px;
  }
  
  .control-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
  
  .control-btn span {
    display: none;
  }
  
  .control-btn.icon-btn {
    padding: 6px;
    min-width: 32px;
  }
  
  .sort-select,
  .filter-select {
    min-width: 120px;
    font-size: 12px;
  }
  
  .view-mode-btn span {
    display: none;
  }
  
  .refresh-btn {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .hierarchy-toolbar {
    background: linear-gradient(135deg, 
      rgba(30, 41, 59, 0.95) 0%, 
      rgba(51, 65, 85, 0.9) 100%);
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .control-label {
    color: #94a3b8;
  }
  
  .control-btn {
    background: rgba(255, 255, 255, 0.1);
    color: #e2e8f0;
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .control-btn:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.15);
  }
  
  .sort-select,
  .filter-select,
  .search-input {
    background: rgba(255, 255, 255, 0.1);
    color: #e2e8f0;
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .view-mode-buttons {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .view-mode-btn {
    color: #94a3b8;
  }
  
  .view-mode-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #e2e8f0;
  }
}
</style>
