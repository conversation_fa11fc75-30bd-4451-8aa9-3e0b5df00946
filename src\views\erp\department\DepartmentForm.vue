<template>
  <Dialog title="部门信息" v-model="dialogVisible" width="600px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="上级部门" prop="parentId">
        <el-tree-select
          v-model="formData.parentId"
          :data="departmentTree"
          :props="{ label: 'deptName', value: 'id' }"
          placeholder="选择上级部门"
          check-strictly
          :render-after-expand="false"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="部门名称" prop="deptName">
        <el-input
          v-model="formData.deptName"
          placeholder="请输入部门名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="部门编码" prop="deptCode">
        <el-input
          v-model="formData.deptCode"
          placeholder="请输入部门编码"
          maxlength="20"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="部门层级" prop="deptLevel">
        <el-select
          v-model="formData.deptLevel"
          placeholder="请选择部门层级"
          style="width: 100%"
        >
          <el-option label="主管部门" :value="1" />
          <el-option label="小组" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="部门类型" prop="deptType">
        <el-select
          v-model="formData.deptType"
          placeholder="请选择部门业务类型"
          style="width: 100%"
        >
          <el-option label="运营部门" value="OPERATION">
            <span style="color: #67c23a">🔵 运营部门</span>
          </el-option>
          <el-option label="运维部门" value="OPS">
            <span style="color: #409eff">🟢 运维部门</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="显示排序" prop="sortOrder">
        <el-input-number
          v-model="formData.sortOrder"
          :min="0"
          :max="9999"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :label="1">正常</el-radio>
          <el-radio :label="0">停用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          placeholder="请输入描述"
          maxlength="200"
          show-word-limit
          :rows="3"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { DepartmentApi, type DepartmentVO, type DepartmentSaveReqVO } from '@/api/erp/department'
import { Dialog } from '@/components/Dialog'

const emit = defineEmits(['success'])

const dialogVisible = ref(false)
const formLoading = ref(false)
const formRef = ref()
const formType = ref('')
const formData = ref<DepartmentSaveReqVO>({
  parentId: 0,
  deptName: '',
  deptCode: '',
  deptLevel: 1,
  deptType: '',
  sortOrder: 0,
  status: 1,
  description: ''
})

const departmentTree = ref<DepartmentVO[]>([])

const formRules = reactive({
  deptName: [
    { required: true, message: '部门名称不能为空', trigger: 'blur' },
    { min: 2, max: 50, message: '部门名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  deptCode: [
    { required: true, message: '部门编码不能为空', trigger: 'blur' },
    { min: 2, max: 20, message: '部门编码长度在 2 到 20 个字符', trigger: 'blur' },
    { pattern: /^[A-Z0-9_]+$/, message: '部门编码只能包含大写字母、数字和下划线', trigger: 'blur' }
  ],
  deptLevel: [
    { required: true, message: '部门层级不能为空', trigger: 'change' }
  ],
  deptType: [
    { required: true, message: '部门类型不能为空', trigger: 'change' }
  ],
  sortOrder: [
    { required: true, message: '显示排序不能为空', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '状态不能为空', trigger: 'change' }
  ]
})

const dialogTitle = computed(() => {
  return formType.value === 'create' ? '新增部门' : '修改部门'
})

/** 打开弹窗 */
const open = async (type: string, id?: number, parentId?: number) => {
  dialogVisible.value = true
  formType.value = type
  resetForm()
  
  // 加载部门树
  await getDepartmentTree()
  
  if (parentId) {
    formData.value.parentId = parentId
  }
  
  if (id) {
    formLoading.value = true
    try {
      const data = await DepartmentApi.getDepartment(id)
      formData.value = data
    } finally {
      formLoading.value = false
    }
  }
}

/** 获取部门树 */
const getDepartmentTree = async () => {
  try {
    const data = await DepartmentApi.getDepartmentTree()
    const rootNode = {
      id: 0,
      deptName: '根部门',
      children: data || []
    }
    departmentTree.value = [rootNode]
  } catch {}
}

/** 提交表单 */
const submitForm = async () => {
  await formRef.value.validate()
  formLoading.value = true
  
  try {
    if (formType.value === 'create') {
      await DepartmentApi.createDepartment(formData.value)
      ElMessage.success('新增成功')
    } else {
      await DepartmentApi.updateDepartment(formData.value)
      ElMessage.success('修改成功')
    }
    dialogVisible.value = false
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    parentId: 0,
    deptName: '',
    deptCode: '',
    deptLevel: 1,
    deptType: '',
    sortOrder: 0,
    status: 1,
    description: ''
  }
  formRef.value?.resetFields()
}

defineExpose({ open })
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 