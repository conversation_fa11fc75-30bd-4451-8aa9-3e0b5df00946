<template>
  <div
    class="hierarchy-node"
    :class="[
      `level-${level}`,
      `node-type-${node.nodeType.toLowerCase()}`,
      { expanded: isExpanded, 'has-children': hasChildren }
    ]"
  >
    <!-- 节点主体卡片 -->
    <div :class="getNodeCardClass()" @click="handleNodeClick">
      <div class="node-content">
        <!-- 左侧：展开/收起按钮 + 节点信息 -->
        <div class="node-left">
          <!-- 展开/收起按钮 -->
          <div class="expand-button" v-if="hasChildren">
            <button
              @click.stop="toggleExpansion"
              class="expand-btn"
              :class="{ expanded: isExpanded }"
            >
              <Icon :icon="isExpanded ? 'ep:arrow-down' : 'ep:arrow-right'" />
            </button>
          </div>
          <div class="expand-placeholder" v-else></div>

          <!-- 节点图标 -->
          <div class="node-icon">
            <Icon :icon="getNodeIcon()" :size="getIconSize()" />
          </div>

          <!-- 节点基本信息 -->
          <div class="node-info">
            <div class="node-header">
              <span class="node-name">{{ node.nodeName }}</span>
              <div class="node-tags">
                <span v-if="node.rank" :class="['rank-tag', getRankTagClass(node.rank)]">
                  第{{ node.rank }}名
                </span>
                <span v-if="node.userRole" :class="['role-tag', getRoleTagClass(node.userRole)]">
                  {{ getRoleText(node.userRole) }}
                </span>
              </div>
            </div>

            <!-- 节点统计信息 -->
            <div v-if="node.statistics && showStatistics" class="node-stats">
              <div class="stat-item">
                <Icon icon="ep:user" />
                <span>{{ node.statistics.directMemberCount }}人</span>
              </div>
              <div
                v-if="node.statistics.totalMemberCount !== node.statistics.directMemberCount"
                class="stat-item"
              >
                <Icon icon="ep:user-filled" />
                <span>总计{{ node.statistics.totalMemberCount }}人</span>
              </div>
              <div class="stat-item">
                <Icon icon="ep:trophy" />
                <span>均{{ formatCurrency(node.statistics.averageAmount) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：业绩金额和操作 -->
        <div class="node-right">
          <!-- 业绩金额 -->
          <div class="amount-section">
            <div class="amount-main">
              <span class="amount-value">{{ formatCurrency(node.amount) }}</span>
              <span class="amount-label">业绩</span>
            </div>

            <!-- 目标和完成率 -->
            <div v-if="node.targetAmount && node.targetAmount > 0" class="amount-progress">
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{
                    width: Math.min(100, (node.amount / node.targetAmount) * 100) + '%',
                    backgroundColor: getProgressColor(node.amount, node.targetAmount)
                  }"
                ></div>
              </div>
              <span class="progress-text">
                {{ ((node.amount / node.targetAmount) * 100).toFixed(1) }}%
              </span>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="node-actions">
            <button
              v-if="node.nodeType !== 'PERSON'"
              class="action-btn primary"
              @click.stop="handleViewDetail"
            >
              <Icon icon="ep:view" />
              <span>详情</span>
            </button>

            <div v-if="hasMoreActions" class="more-actions" @click.stop>
              <button class="action-btn more-btn">
                <Icon icon="ep:more" />
              </button>
              <div class="dropdown-menu">
                <button class="dropdown-item" @click="handleExport">
                  <Icon icon="ep:download" />
                  <span>导出数据</span>
                </button>
                <button
                  v-if="node.nodeType !== 'PERSON'"
                  class="dropdown-item"
                  @click="handleAnalyze"
                >
                  <Icon icon="ep:data-analysis" />
                  <span>数据分析</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 展开式内容：进度条和趋势 -->
      <div v-if="isExpanded && showExpandedContent" class="expanded-content">
        <div class="performance-breakdown">
          <!-- 业绩构成分析 -->
          <div class="breakdown-section">
            <h4 class="section-title">
              <Icon icon="ep:pie-chart" />
              <span>业绩构成分析</span>
            </h4>
            <div class="breakdown-grid">
              <div class="breakdown-item">
                <div class="breakdown-info">
                  <span class="breakdown-label">总业绩</span>
                  <span class="breakdown-percent">100%</span>
                </div>
                <span class="breakdown-value">{{ formatCurrency(node.amount) }}</span>
              </div>
            </div>
          </div>

          <!-- 同级排名对比 -->
          <div v-if="node.statistics" class="ranking-section">
            <h4 class="section-title">
              <Icon icon="ep:medal" />
              <span>同级排名对比</span>
            </h4>
            <div class="ranking-info">
              <div class="ranking-circle">
                <div
                  class="circle-progress"
                  :style="{
                    background: `conic-gradient(${getRankingColor(node.statistics.peerRank, node.statistics.peerTotal)} ${(node.statistics.peerRank / node.statistics.peerTotal) * 360}deg, #f0f0f0 0deg)`
                  }"
                >
                  <div class="circle-inner">
                    <span class="ranking-text">
                      {{ node.statistics.peerRank }}/{{ node.statistics.peerTotal }}
                    </span>
                  </div>
                </div>
              </div>
              <div class="ranking-details">
                <p class="ranking-desc-main"
                  >在同级{{ node.statistics.peerTotal }}个{{ getNodeTypeText() }}中排名第{{
                    node.statistics.peerRank
                  }}位</p
                >
                <p class="ranking-desc-sub">{{ getRankingDescription() }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 子节点递归渲染 -->
    <div v-if="isExpanded && hasChildren" class="children-container">
      <div class="children-connector"></div>
      <div class="children-list">
        <HierarchyNode
          v-for="child in node.children"
          :key="child.nodeId"
          :node="child"
          :level="level + 1"
          :user-role="userRole"
          :view-mode="viewMode"
          :expanded-nodes="expandedNodes"
          @expand="$emit('expand', $event)"
          @collapse="$emit('collapse', $event)"
          @view-detail="$emit('view-detail', $event, child.nodeType)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { type PerformanceNodeVO } from '@/api/erp/performance'

interface Props {
  node: PerformanceNodeVO
  level: number
  userRole: string
  viewMode?: string
  expandedNodes?: Set<string>
}

interface Emits {
  (e: 'expand', nodeId: string): void
  (e: 'collapse', nodeId: string): void
  (e: 'view-detail', nodeId: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 使用外部传入的展开状态，或者使用本地状态
const isExpanded = computed(() => {
  if (props.expandedNodes) {
    return props.expandedNodes.has(props.node.nodeId)
  }
  return props.node.defaultExpanded ?? false
})

/** 是否有子节点 */
const hasChildren = computed(() => {
  return props.node.children && props.node.children.length > 0
})

/** 是否显示统计信息 */
const showStatistics = computed(() => {
  return props.node.nodeType !== 'PERSON' && props.level < 2
})

/** 是否显示展开内容 */
const showExpandedContent = computed(() => {
  return props.node.nodeType !== 'PERSON'
})

/** 是否有更多操作 */
const hasMoreActions = computed(() => {
  return props.userRole === 'SUPER_ADMIN' || props.userRole === 'SUPERVISOR'
})

/** 获取节点卡片样式类 */
const getNodeCardClass = () => {
  const classes = ['node-card']
  classes.push(`node-${props.node.nodeType.toLowerCase()}`)
  if (props.level === 0) classes.push('root-node')
  if (isExpanded.value) classes.push('expanded')
  return classes.join(' ')
}

/** 获取节点图标 */
const getNodeIcon = () => {
  switch (props.node.nodeType) {
    case 'DEPARTMENT':
      return 'ep:office-building'
    case 'GROUP':
      return 'ep:user-filled'
    case 'PERSON':
      return 'ep:user'
    default:
      return 'ep:folder'
  }
}

/** 获取图标大小 */
const getIconSize = () => {
  switch (props.level) {
    case 0:
      return 24
    case 1:
      return 20
    default:
      return 18
  }
}

/** 获取排名标签样式类 */
const getRankTagClass = (rank: number) => {
  if (rank === 1) return 'rank-first'
  if (rank === 2) return 'rank-second'
  if (rank === 3) return 'rank-third'
  return 'rank-normal'
}

/** 获取角色标签样式类 */
const getRoleTagClass = (role: string) => {
  switch (role) {
    case 'SUPER_ADMIN':
      return 'role-admin'
    case 'SUPERVISOR':
      return 'role-supervisor'
    case 'TEAM_LEADER':
      return 'role-leader'
    case 'TEAM_MEMBER':
      return 'role-member'
    default:
      return 'role-default'
  }
}

/** 获取角色文本 */
const getRoleText = (role: string) => {
  switch (role) {
    case 'SUPER_ADMIN':
      return '超管'
    case 'SUPERVISOR':
      return '主管'
    case 'TEAM_LEADER':
      return '组长'
    case 'TEAM_MEMBER':
      return '组员'
    default:
      return '未知'
  }
}

/** 获取节点类型文本 */
const getNodeTypeText = () => {
  switch (props.node.nodeType) {
    case 'DEPARTMENT':
      return '部门'
    case 'GROUP':
      return '小组'
    case 'PERSON':
      return '成员'
    default:
      return '节点'
  }
}

/** 格式化金额 */
const formatCurrency = (amount: number) => {
  return amount.toLocaleString()
}

/** 获取进度条颜色 */
const getProgressColor = (current: number, target: number) => {
  const percentage = (current / target) * 100
  if (percentage >= 100) return '#10b981'
  if (percentage >= 80) return '#f59e0b'
  if (percentage >= 60) return '#ef4444'
  return '#6b7280'
}

/** 获取排名颜色 */
const getRankingColor = (rank: number, total: number) => {
  const ratio = rank / total
  if (ratio <= 0.2) return '#10b981' // 前20%绿色
  if (ratio <= 0.5) return '#f59e0b' // 前50%橙色
  return '#ef4444' // 后50%红色
}

/** 获取直属业绩 */
const getDirectAmount = () => {
  if (!hasChildren.value) return props.node.amount
  const childrenAmount = props.node.children?.reduce((sum, child) => sum + child.amount, 0) || 0
  return Math.max(0, props.node.amount - childrenAmount)
}

/** 获取下级业绩 */
const getSubordinateAmount = () => {
  return props.node.children?.reduce((sum, child) => sum + child.amount, 0) || 0
}

/** 获取直属业绩占比 */
const getDirectPercentage = () => {
  if (props.node.amount === 0) return 0
  return ((getDirectAmount() / props.node.amount) * 100).toFixed(1)
}

/** 获取下级业绩占比 */
const getSubordinatePercentage = () => {
  if (props.node.amount === 0) return 0
  return ((getSubordinateAmount() / props.node.amount) * 100).toFixed(1)
}

/** 获取排名描述 */
const getRankingDescription = () => {
  if (!props.node.statistics) return ''
  const { peerRank, peerTotal } = props.node.statistics
  const ratio = peerRank / peerTotal

  if (ratio <= 0.2) return '表现优秀，继续保持！'
  if (ratio <= 0.5) return '表现良好，可以更进一步'
  if (ratio <= 0.8) return '表现一般，需要努力提升'
  return '表现需要改进，加油！'
}

/** 切换展开状态 */
const toggleExpansion = () => {
  if (isExpanded.value) {
    emit('collapse', props.node.nodeId)
  } else {
    emit('expand', props.node.nodeId)
  }
}

/** 处理节点点击 */
const handleNodeClick = () => {
  if (hasChildren.value) {
    toggleExpansion()
  }
}

/** 处理查看详情 */
const handleViewDetail = () => {
  emit('view-detail', props.node.nodeId)
}

/** 处理导出 */
const handleExport = () => {
  console.log('导出节点数据:', props.node.nodeId)
  // TODO: 实现导出逻辑
}

/** 处理分析 */
const handleAnalyze = () => {
  console.log('分析节点数据:', props.node.nodeId)
  // TODO: 实现分析逻辑
}
</script>

<style scoped>
.hierarchy-node {
  position: relative;
  margin-bottom: 16px;
}

/* 节点卡片样式 - 现代简约风格 */
.node-card {
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.node-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.node-card.expanded {
  border-color: rgba(99, 102, 241, 0.2);
  box-shadow: 0 8px 30px rgba(99, 102, 241, 0.15);
}

/* 根据节点类型设置不同的现代化样式 */
.node-card.node-department {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.node-card.node-department::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
}

.node-card.node-group {
  background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.node-card.node-group::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.6) 50%,
    rgba(255, 255, 255, 0.3) 100%
  );
}

.node-card.node-person {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  border: 1px solid rgba(99, 102, 241, 0.1);
  color: #1e293b;
}

.node-card.node-person::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(99, 102, 241, 0.3) 50%, transparent 100%);
}

/* 节点内容布局 */
.node-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px;
  position: relative;
  z-index: 1;
}

.node-left {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 16px;
}

.expand-button {
  width: 32px;
  display: flex;
  justify-content: center;
}

.expand-placeholder {
  width: 32px;
}

.expand-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.15);
  color: currentColor;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.expand-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

.expand-btn.expanded {
  transform: rotate(0deg);
}

.node-person .expand-btn {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.node-person .expand-btn:hover {
  background: rgba(99, 102, 241, 0.2);
}

.node-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.node-department .node-icon,
.node-group .node-icon {
  background: rgba(255, 255, 255, 0.15);
  color: white;
}

.node-person .node-icon {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(168, 85, 247, 0.08) 100%);
  color: #6366f1;
}

.node-info {
  flex: 1;
}

.node-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.node-name {
  font-size: 18px;
  font-weight: 600;
  line-height: 1.4;
  letter-spacing: -0.01em;
}

.node-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.rank-tag,
.role-tag {
  font-size: 11px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 6px;
  line-height: 1;
}

/* 排名标签样式 */
.rank-first {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.rank-second {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.rank-third {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.rank-normal {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

/* 角色标签样式 */
.role-admin {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.role-supervisor {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.role-leader {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.role-member {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.role-default {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

.node-stats {
  display: flex;
  gap: 16px;
  font-size: 13px;
  font-weight: 500;
  opacity: 0.85;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 3px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  backdrop-filter: blur(5px);
}

.node-person .stat-item {
  background: rgba(99, 102, 241, 0.08);
  color: #6366f1;
}

/* 右侧金额和操作 */
.node-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.amount-section {
  text-align: right;
}

.amount-main {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.amount-value {
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.amount-label {
  font-size: 12px;
  font-weight: 500;
  opacity: 0.7;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.amount-progress {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  width: 60px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.node-person .progress-bar {
  background: #e5e7eb;
}

.progress-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 11px;
  font-weight: 600;
  opacity: 0.8;
}

.node-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.15);
  color: currentColor;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
}

.node-person .action-btn {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.node-person .action-btn:hover {
  background: rgba(99, 102, 241, 0.2);
}

.more-actions {
  position: relative;
}

.more-btn {
  padding: 6px;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 4px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
  min-width: 120px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-4px);
  transition: all 0.2s ease;
  z-index: 10;
}

.more-actions:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px 12px;
  border: none;
  background: none;
  color: #374151;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background: #f3f4f6;
}

.dropdown-item:first-child {
  border-radius: 8px 8px 0 0;
}

.dropdown-item:last-child {
  border-radius: 0 0 8px 8px;
}

/* 展开内容样式 */
.expanded-content {
  border-top: 1px solid rgba(255, 255, 255, 0.15);
  padding: 20px 28px;
  margin-top: 0;
}

.node-person .expanded-content {
  border-top-color: #e5e7eb;
}

.performance-breakdown {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  opacity: 0.9;
}

.breakdown-section .breakdown-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.node-person .breakdown-item {
  background: #f8fafc;
  color: #374151;
}

.breakdown-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.breakdown-label {
  font-size: 12px;
  opacity: 0.8;
}

.breakdown-percent {
  font-size: 11px;
  opacity: 0.6;
}

.breakdown-value {
  font-weight: 600;
  font-size: 14px;
}

.ranking-section .ranking-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.ranking-circle {
  width: 60px;
  height: 60px;
  flex-shrink: 0;
}

.circle-progress {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.circle-inner {
  width: 80%;
  height: 80%;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.node-person .circle-inner {
  background: #f8fafc;
}

.ranking-text {
  font-size: 11px;
  font-weight: 600;
  color: #374151;
}

.ranking-details {
  flex: 1;
}

.ranking-desc-main {
  margin: 0 0 4px 0;
  font-size: 12px;
  line-height: 1.4;
  opacity: 0.9;
}

.ranking-desc-sub {
  margin: 0;
  font-size: 11px;
  opacity: 0.7;
}

/* 子节点容器 */
.children-container {
  position: relative;
  margin-left: 32px;
  margin-top: 12px;
}

.children-connector {
  position: absolute;
  left: -16px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, #e5e7eb 0%, transparent 100%);
  border-radius: 1px;
}

.children-connector::before {
  content: '';
  position: absolute;
  top: -12px;
  left: -6px;
  width: 14px;
  height: 2px;
  background: #e5e7eb;
  border-radius: 1px;
}

.children-list {
  position: relative;
}

/* 层级样式 */
.level-0 .node-card {
  font-size: 16px;
}

.level-1 .node-card {
  font-size: 15px;
}

.level-2 .node-card {
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .node-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .node-left {
    justify-content: flex-start;
  }

  .node-right {
    justify-content: space-between;
    align-self: stretch;
  }

  .performance-breakdown {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .node-content {
    padding: 20px 24px;
  }

  .node-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .node-stats {
    flex-direction: column;
    gap: 8px;
  }

  .amount-section {
    text-align: left;
  }

  .amount-main {
    align-items: flex-start;
  }

  .expanded-content {
    padding: 16px 20px;
  }
}

@media (max-width: 480px) {
  .node-left {
    gap: 12px;
  }

  .node-icon {
    width: 36px;
    height: 36px;
  }

  .node-name {
    font-size: 16px;
  }

  .amount-value {
    font-size: 20px;
  }

  .children-container {
    margin-left: 24px;
  }
}
</style>
