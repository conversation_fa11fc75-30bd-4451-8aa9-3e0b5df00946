import request from '@/config/axios'

// 层级化业绩节点类型定义
export interface PerformanceNodeVO {
  nodeId: string
  nodeName: string
  nodeType: 'DEPARTMENT' | 'GROUP' | 'PERSON'
  userRole?: string
  amount: number
  rank?: number
  targetAmount?: number
  completionRate?: number
  defaultExpanded?: boolean
  children?: PerformanceNodeVO[]
  statistics?: {
    directMemberCount: number
    totalMemberCount: number
    averageAmount: number
    growthRate: number
    peerRank: number
    peerTotal: number
  }
}

// 业绩数据响应类型（更新后支持层级化数据）
export interface PerformanceDataVO {
  departmentTotal?: number
  // 新增层级化数据字段
  hierarchyData?: PerformanceNodeVO[]
  // 保持向后兼容的产品分组数据
  productBreakdown?: Array<{
    productId: number
    productName: string
    totalAmount: number
    teamData?: Array<{
      teamId: string
      teamName: string
      amount: number
      memberCount: number
      members?: Array<{
        userId: string
        userName: string
        amount: number
        rank: number
      }>
    }>
    personalData?: {
      userId: string
      userName: string
      amount: number
      rank: number
      targetAmount?: number
      completionRate?: number
    }
  }>
}

// 业绩查询请求参数
export interface PerformancePageReqVO {
  startDate?: string // 开始日期 YYYY-MM-DD 格式
  endDate?: string   // 结束日期 YYYY-MM-DD 格式
  productIds?: number[] // 产品ID列表
}

// 业绩管理API（基于资源绑定权限，使用统一的SQL-based权限过滤）
export const PerformanceApi = {
  // 获取用户角色类型
  getUserRole: (): Promise<string> => {
    return request.get({ url: '/erp/performance/role' })
  },

  // 统一的业绩数据接口（后端根据用户角色和SQL权限过滤自动返回相应数据）
  // 后端已统一采用SQL-based权限过滤，不再区分多个接口
  // 权限逻辑：运维人员基于产品+渠道+区服，运营人员基于产品+渠道
  getPerformanceData: (data: PerformancePageReqVO): Promise<PerformanceDataVO> => {
    return request.get({ url: '/erp/performance/data', params: data })
  },

  // 获取业绩总览数据
  getPerformanceOverview: (data: PerformancePageReqVO): Promise<PerformanceDataVO> => {
    return request.get({ url: '/erp/performance/overview', params: data })
  },

  // 获取部门详细业绩数据
  getDepartmentDetail: (departmentId: string, data: PerformancePageReqVO): Promise<PerformanceDataVO> => {
    return request.get({ url: `/erp/performance/department/${departmentId}/detail`, params: data })
  },

  // 获取组详细业绩数据
  getTeamDetail: (teamId: string, data: PerformancePageReqVO): Promise<PerformanceDataVO> => {
    return request.get({ url: `/erp/performance/team/${teamId}/detail`, params: data })
  },

  // 以下接口保留用于兼容（实际后端会使用统一的权限SQL过滤）
  // 获取主管业绩数据（兼容接口，内部调用统一方法）
  getSupervisorData: (data: PerformancePageReqVO): Promise<PerformanceDataVO> => {
    return request.get({ url: '/erp/performance/supervisor-data', params: data })
  },

  // 获取组长业绩数据（兼容接口，内部调用统一方法）
  getTeamLeaderData: (data: PerformancePageReqVO): Promise<PerformanceDataVO> => {
    return request.get({ url: '/erp/performance/team-leader-data', params: data })
  },

  // 获取个人业绩数据（兼容接口，内部调用统一方法）
  getPersonalData: (data: PerformancePageReqVO): Promise<PerformanceDataVO> => {
    return request.get({ url: '/erp/performance/personal-data', params: data })
  }
}