<template>
  <Dialog
    v-model="dialogVisible"
    :title="`${selectedDept?.deptName || ''} - 用户管理`"
    width="1600px"
    max-height="900px"
  >
    <el-tabs v-model="activeTab" type="border-card">
      <!-- 用户分配Tab -->
      <el-tab-pane label="用户分配" name="users">
        <div class="user-management">
          <el-row :gutter="20">
            <!-- 左侧：已分配用户列表 -->
            <el-col :span="12">
              <div class="user-section">
                <div class="section-header">
                  <h4>已分配用户</h4>
                  <div class="header-actions">
                    <el-tag
                      :type="getRelationTypeTagType(getCurrentRelationType())"
                      size="small"
                      class="dept-relation-tag"
                    >
                      当前部门：{{ getRelationTypeText(getCurrentRelationType()) }}
                    </el-tag>
                    <el-tag
                      :type="getRoleTagType(getAutoRoleByDepartment())"
                      size="small"
                      class="dept-auto-role-tag"
                      style="margin-left: 8px"
                    >
                      自动角色：{{ getRoleText(getAutoRoleByDepartment()) }}
                    </el-tag>
                    <el-button type="primary" size="small" @click="openUserSelector">
                      添加用户
                    </el-button>
                  </div>
                </div>
                <el-table :data="assignedUsers" v-loading="assignedLoading" height="450" border>
                  <el-table-column label="用户ID" prop="userId" width="80" />
                  <el-table-column label="用户名" prop="userName" min-width="120" />
                  <el-table-column label="关系类型" prop="relationType" width="100">
                    <template #default="scope">
                      <el-tag size="small" :type="getRelationTypeTagType(scope.row.relationType)">
                        {{ getRelationTypeText(scope.row.relationType) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="部门角色" prop="role" width="100">
                    <template #default="scope">
                      <el-tag size="small" :type="getRoleTagType(scope.row.role)">
                        {{ getRoleText(scope.row.role) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="150">
                    <template #default="scope">
                      <el-button-group>
                        <el-button
                          type="info"
                          size="small"
                          @click="viewUserPermissions(scope.row.userId!)"
                          title="查看权限"
                        >
                          <el-icon><View /></el-icon>
                        </el-button>
                        <el-button
                          type="danger"
                          size="small"
                          @click="removeUser(scope.row.userId!)"
                          title="移除用户"
                        >
                          <el-icon><Delete /></el-icon>
                        </el-button>
                      </el-button-group>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-col>

            <!-- 右侧：用户选择器 -->
            <el-col :span="12">
              <div class="user-section">
                <div class="section-header">
                  <h4>用户选择</h4>
                  <el-input
                    v-model="userSearchKeyword"
                    placeholder="搜索用户名或昵称..."
                    size="small"
                    @input="searchUsers"
                    clearable
                    style="width: 200px"
                  />
                </div>
                <el-table
                  :data="availableUsers"
                  v-loading="availableLoading"
                  height="350"
                  border
                  @selection-change="handleSelectionChange"
                >
                  <el-table-column type="selection" width="55" />
                  <el-table-column label="用户ID" prop="id" width="80" />
                  <el-table-column label="用户名" prop="username" min-width="120" />
                  <el-table-column label="昵称" prop="nickname" min-width="120" />
                  <el-table-column label="状态" prop="status" width="80">
                    <template #default="scope">
                      <el-tag size="small" :type="scope.row.status === 0 ? 'success' : 'danger'">
                        {{ scope.row.status === 0 ? '正常' : '禁用' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                </el-table>

                <!-- 添加分页控制 -->
                <div class="mt-4 flex justify-between items-center">
                  <div>
                    <el-button
                      :type="getAssignButtonType()"
                      @click="batchAssignUsers"
                      :disabled="selectedUsers.length === 0"
                    >
                      <template v-if="selectedDept?.deptLevel === 2">
                        选择角色分配 ({{ selectedUsers.length }}) -
                        {{ getCurrentRelationTypeText() }}
                      </template>
                      <template v-else>
                        批量分配 ({{ selectedUsers.length }}) - {{ getCurrentRelationTypeText() }}
                      </template>
                    </el-button>
                  </div>
                  <div>
                    <el-pagination
                      v-model:currentPage="userPageParams.pageNo"
                      v-model:page-size="userPageParams.pageSize"
                      :page-sizes="[10, 20, 50, 100]"
                      :total="userPageTotal"
                      layout="total, sizes, prev, pager, next, jumper"
                      @size-change="handleUserPageSizeChange"
                      @current-change="handleUserPageChange"
                      small
                    />
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>

      <!-- 资源绑定Tab -->
      <el-tab-pane label="资源绑定" name="resources">
        <div class="resource-binding-modern">
          <!-- 用户选择器 -->
          <div class="user-selector-card">
            <el-card shadow="always">
              <template #header>
                <div class="card-header">
                  <span class="card-title">用户资源绑定管理</span>
                  <el-tag v-if="selectedUserId" type="primary" size="large">
                    {{ getSelectedUserName() }}
                  </el-tag>
                </div>
              </template>

              <el-form :inline="true" label-width="100px">
                <el-form-item label="选择用户">
                  <el-select
                    v-model="selectedUserId"
                    placeholder="请选择要配置资源的用户"
                    clearable
                    style="width: 320px"
                    @change="handleResourceUserChange"
                  >
                    <el-option
                      v-for="user in assignedUsers"
                      :key="user.userId"
                      :label="user.userName || user.username || '用户' + user.userId"
                      :value="user.userId!"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item>
                  <el-button type="primary" @click="addNewBinding" :disabled="!selectedUserId">
                    <el-icon class="mr-1"><Plus /></el-icon>
                    添加资源绑定
                  </el-button>
                  <el-button
                    type="danger"
                    @click="batchDeleteBindings"
                    :disabled="!selectedUserId || selectedBindings.length === 0"
                  >
                    <el-icon class="mr-1"><Delete /></el-icon>
                    批量删除 ({{ selectedBindings.length }})
                  </el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </div>

          <!-- 资源绑定列表 -->
          <div v-if="selectedUserId" class="resource-bindings-list">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span class="card-title">{{ getSelectedUserName() }} 的资源绑定</span>
                  <div class="header-actions">
                    <!--                    <el-button type="success" size="small" @click="batchAddBindings">-->
                    <!--                      <el-icon class="mr-1"><Plus /></el-icon>-->
                    <!--                      批量添加-->
                    <!--                    </el-button>-->
                    <el-button type="primary" size="small" @click="refreshBindings">
                      <el-icon class="mr-1"><Refresh /></el-icon>
                      刷新
                    </el-button>
                  </div>
                </div>
              </template>

              <el-table
                :data="userResourceBindings"
                v-loading="bindingLoading"
                @selection-change="handleBindingSelectionChange"
                height="500"
                border
                stripe
              >
                <el-table-column type="selection" width="55" />
                <el-table-column label="资源类型" prop="resourceType" width="120">
                  <template #default="scope">
                    <el-tag :type="getResourceTypeTagType(scope.row.resourceType)" size="small">
                      {{ getResourceTypeText(scope.row.resourceType) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="产品" prop="productId" width="150">
                  <template #default="scope">
                    <span v-if="scope.row.productId">
                      <el-tag type="primary" effect="plain" size="small">
                        {{ getProductName(scope.row.productId) }}
                      </el-tag>
                    </span>
                    <span v-else class="text-gray-400">-</span>
                  </template>
                </el-table-column>
                <el-table-column label="渠道" prop="channelCode" width="120">
                  <template #default="scope">
                    <span v-if="scope.row.channelCode">
                      <el-tag type="success" effect="plain" size="small">
                        {{ scope.row.channelCode }}
                      </el-tag>
                    </span>
                    <span v-else class="text-gray-400">-</span>
                  </template>
                </el-table-column>

                <el-table-column label="区服" prop="serverName" width="120">
                  <template #default="scope">
                    <span v-if="scope.row.serverName">
                      <el-tag type="danger" effect="plain" size="small">
                        {{ scope.row.serverName }}
                      </el-tag>
                    </span>
                    <span v-else class="text-gray-400">-</span>
                  </template>
                </el-table-column>
                <el-table-column label="目标部门" prop="targetDeptId" width="120">
                  <template #default="scope">
                    <span v-if="scope.row.targetDeptId">
                      {{ getDeptName(scope.row.targetDeptId) }}
                    </span>
                    <span v-else class="text-gray-400">-</span>
                  </template>
                </el-table-column>
                <el-table-column label="状态" prop="status" width="80">
                  <template #default="scope">
                    <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="small">
                      {{ scope.row.status === 1 ? '启用' : '停用' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <!--                <el-table-column label="备注" prop="remark" min-width="150" />-->
                <el-table-column label="操作" width="150" fixed="right">
                  <template #default="scope">
                    <el-button-group>
                      <el-button type="primary" size="small" @click="editBinding(scope.row)">
                        <el-icon><Edit /></el-icon>
                      </el-button>
                      <el-button type="danger" size="small" @click="deleteBinding(scope.row.id)">
                        <el-icon><Delete /></el-icon>
                      </el-button>
                      <el-button type="info" size="small" @click="duplicateBinding(scope.row)">
                        <el-icon><DocumentCopy /></el-icon>
                      </el-button>
                    </el-button-group>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-state-modern">
            <div class="empty-illustration">
              <el-icon size="80" color="#ddd"><UserFilled /></el-icon>
            </div>
            <h3 class="empty-title">请选择用户进行资源绑定配置</h3>
            <p class="empty-description"
              >选择用户后，您可以为其配置产品、渠道、运营队伍等资源权限</p
            >
          </div>
        </div>
      </el-tab-pane>

      <!-- 权限预览Tab -->
      <el-tab-pane label="权限预览" name="preview">
        <div class="permission-preview-modern">
          <!-- 用户选择器 -->
          <div class="user-selector-card">
            <el-card shadow="always">
              <template #header>
                <div class="card-header">
                  <span class="card-title">用户权限预览</span>
                  <el-tag v-if="previewUserId" type="success" size="large">
                    {{ getPreviewUserName() }}
                  </el-tag>
                </div>
              </template>

              <el-form :inline="true" label-width="100px">
                <el-form-item label="选择用户">
                  <el-select
                    v-model="previewUserId"
                    placeholder="请选择要预览权限的用户"
                    clearable
                    style="width: 320px"
                    @change="loadUserPermissionPreview"
                  >
                    <el-option
                      v-for="user in assignedUsers"
                      :key="user.userId"
                      :label="user.userName || user.username || '用户' + user.userId"
                      :value="user.userId!"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item>
                  <el-button
                    type="primary"
                    @click="loadUserPermissionPreview"
                    :disabled="!previewUserId"
                  >
                    <el-icon class="mr-1"><Refresh /></el-icon>
                    刷新权限
                  </el-button>
                  <el-button type="info" @click="exportPermissionReport" :disabled="!previewUserId">
                    <el-icon class="mr-1"><Download /></el-icon>
                    导出报告
                  </el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </div>

          <!-- 权限预览内容 -->
          <div v-if="previewUserId && permissionPreview" class="permission-content-modern">
            <!-- 权限错误状态 -->
            <div v-if="permissionPreview.error" class="error-state-modern">
              <el-result icon="error" title="权限加载失败" :sub-title="permissionPreview.error">
                <template #extra>
                  <el-button type="primary" @click="loadUserPermissionPreview">重新加载</el-button>
                </template>
              </el-result>
            </div>

            <!-- 权限正常状态 -->
            <div v-else>
              <!-- 权限统计卡片 -->
              <div class="permission-stats-row">
                <el-row :gutter="20">
                  <el-col :span="6">
                    <div class="stat-card stat-card-primary">
                      <div class="stat-icon">
                        <el-icon size="32"><User /></el-icon>
                      </div>
                      <div class="stat-content">
                        <div class="stat-title">用户信息</div>
                        <div class="stat-value">{{ getPreviewUserName() }}</div>
                        <div class="stat-desc">{{ selectedDept?.deptName }}</div>
                      </div>
                    </div>
                  </el-col>

                  <el-col :span="6">
                    <div class="stat-card stat-card-success">
                      <div class="stat-icon">
                        <el-icon size="32"><Connection /></el-icon>
                      </div>
                      <div class="stat-content">
                        <div class="stat-title">权限绑定</div>
                        <div class="stat-value">{{ permissionPreview.totalBindings || 0 }}</div>
                        <div class="stat-desc">个权限绑定</div>
                      </div>
                    </div>
                  </el-col>

                  <el-col :span="6">
                    <div class="stat-card stat-card-warning">
                      <div class="stat-icon">
                        <el-icon size="32"><Box /></el-icon>
                      </div>
                      <div class="stat-content">
                        <div class="stat-title">产品权限</div>
                        <div class="stat-value">{{ permissionPreview.productCount || 0 }}</div>
                        <div class="stat-desc">个产品可访问</div>
                      </div>
                    </div>
                  </el-col>

                  <el-col :span="6">
                    <div class="stat-card stat-card-danger">
                      <div class="stat-icon">
                        <el-icon size="32"><Share /></el-icon>
                      </div>
                      <div class="stat-content">
                        <div class="stat-title">渠道权限</div>
                        <div class="stat-value">{{ permissionPreview.channelCount || 0 }}</div>
                        <div class="stat-desc">个渠道可访问</div>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <!-- 权限类型分布 -->
              <div class="permission-distribution">
                <el-card shadow="never">
                  <template #header>
                    <div class="card-header">
                      <span class="card-title">权限类型分布</span>
                      <el-tag :type="permissionPreview.hasBindings ? 'success' : 'warning'">
                        {{ permissionPreview.hasBindings ? '已配置权限' : '未配置权限' }}
                      </el-tag>
                    </div>
                  </template>

                  <div class="permission-types-grid">
                    <div
                      v-for="(count, type) in getPermissionTypeStats()"
                      :key="type"
                      class="permission-type-card"
                      :class="count > 0 ? 'has-permissions' : 'no-permissions'"
                    >
                      <div class="type-icon">
                        <el-icon :size="24">
                          <Box v-if="type === 'PRODUCT_CHANNEL'" />
                          <UserFilled v-else-if="type === 'OPERATION_TEAM'" />
                          <Monitor v-else-if="type === 'SERVER'" />
                        </el-icon>
                      </div>
                      <div class="type-content">
                        <div class="type-name">{{ getResourceTypeText(String(type)) }}</div>
                        <div class="type-count">{{ count }} 个权限</div>
                      </div>
                    </div>
                  </div>
                </el-card>
              </div>

              <!-- 权限详情 -->
              <div v-if="permissionPreview.hasBindings" class="permission-details-modern">
                <el-card shadow="never">
                  <template #header>
                    <div class="card-header">
                      <span class="card-title">权限详情</span>
                      <div class="header-actions">
                        <el-button
                          type="primary"
                          size="small"
                          @click="activePermissionTab = 'summary'"
                        >
                          <el-icon class="mr-1"><PieChart /></el-icon>
                          汇总视图
                        </el-button>
                        <el-button type="info" size="small" @click="activePermissionTab = 'debug'">
                          <el-icon class="mr-1"><Monitor /></el-icon>
                          调试信息
                        </el-button>
                      </div>
                    </div>
                  </template>

                  <el-tabs v-model="activePermissionTab" type="border-card" class="permission-tabs">
                    <!-- 权限汇总 -->
                    <el-tab-pane label="权限汇总" name="summary">
                      <div class="permission-summary-grid">
                        <el-row :gutter="20">
                          <el-col :span="12">
                            <div class="resource-category">
                              <div class="category-header">
                                <el-icon class="category-icon"><Box /></el-icon>
                                <span class="category-title">可访问产品</span>
                                <el-badge
                                  :value="permissionPreview.boundProductIds?.length || 0"
                                  class="category-badge"
                                />
                              </div>
                              <div class="resource-tags">
                                <el-tag
                                  v-for="productId in permissionPreview.boundProductIds"
                                  :key="productId"
                                  class="resource-tag"
                                  type="primary"
                                  effect="light"
                                  size="large"
                                >
                                  {{ getProductName(productId) }}
                                </el-tag>
                                <div
                                  v-if="!permissionPreview.boundProductIds?.length"
                                  class="empty-resources"
                                >
                                  <el-icon class="empty-icon"><Warning /></el-icon>
                                  <span>暂无产品权限</span>
                                </div>
                              </div>
                            </div>
                          </el-col>

                          <el-col :span="12">
                            <div class="resource-category">
                              <div class="category-header">
                                <el-icon class="category-icon"><Share /></el-icon>
                                <span class="category-title">可访问渠道</span>
                                <el-badge
                                  :value="permissionPreview.boundChannelCodes?.length || 0"
                                  class="category-badge"
                                />
                              </div>
                              <div class="resource-tags">
                                <el-tag
                                  v-for="channelCode in permissionPreview.boundChannelCodes"
                                  :key="channelCode"
                                  class="resource-tag"
                                  type="success"
                                  effect="light"
                                  size="large"
                                >
                                  {{ channelCode }}
                                </el-tag>
                                <div
                                  v-if="!permissionPreview.boundChannelCodes?.length"
                                  class="empty-resources"
                                >
                                  <el-icon class="empty-icon"><Warning /></el-icon>
                                  <span>暂无渠道权限</span>
                                </div>
                              </div>
                            </div>
                          </el-col>
                        </el-row>

                        <el-row :gutter="20" class="mt-4" v-if="isOpsDept()">
                          <el-col :span="12">
                            <div class="resource-category">
                              <div class="category-header">
                                <el-icon class="category-icon"><UserFilled /></el-icon>
                                <span class="category-title">可管理运营队伍</span>
                                <el-badge
                                  :value="permissionPreview.boundOperationTeams?.length || 0"
                                  class="category-badge"
                                />
                              </div>
                              <div class="resource-tags">
                                <el-tag
                                  v-for="team in permissionPreview.boundOperationTeams"
                                  :key="team"
                                  class="resource-tag"
                                  type="warning"
                                  effect="light"
                                  size="large"
                                >
                                  {{ team }}
                                </el-tag>
                                <div
                                  v-if="!permissionPreview.boundOperationTeams?.length"
                                  class="empty-resources"
                                >
                                  <el-icon class="empty-icon"><Warning /></el-icon>
                                  <span>暂无运营队伍权限</span>
                                </div>
                              </div>
                            </div>
                          </el-col>

                          <el-col :span="12">
                            <div class="resource-category">
                              <div class="category-header">
                                <el-icon class="category-icon"><Monitor /></el-icon>
                                <span class="category-title">可管理区服</span>
                                <el-badge
                                  :value="permissionPreview.boundServerNames?.length || 0"
                                  class="category-badge"
                                />
                              </div>
                              <div class="resource-tags scrollable-tags">
                                <el-tag
                                  v-for="server in permissionPreview.boundServerNames"
                                  :key="server"
                                  class="resource-tag"
                                  type="danger"
                                  effect="light"
                                  size="large"
                                >
                                  {{ server }}
                                </el-tag>
                                <div
                                  v-if="!permissionPreview.boundServerNames?.length"
                                  class="empty-resources"
                                >
                                  <el-icon class="empty-icon"><Warning /></el-icon>
                                  <span>暂无区服权限</span>
                                </div>
                              </div>
                            </div>
                          </el-col>
                        </el-row>
                      </div>
                    </el-tab-pane>

                    <!-- 产品渠道权限 -->
                    <el-tab-pane
                      v-if="permissionPreview.bindingsByType?.PRODUCT_CHANNEL?.length > 0"
                      label="产品渠道权限"
                      name="product-channel"
                    >
                      <div class="permission-table">
                        <el-table
                          :data="permissionPreview.bindingsByType.PRODUCT_CHANNEL"
                          border
                          stripe
                          max-height="400"
                        >
                          <el-table-column label="产品名称" width="200">
                            <template #default="scope">
                              <el-tag type="primary" effect="light">
                                {{ getProductName(scope.row.productId) }}
                              </el-tag>
                            </template>
                          </el-table-column>
                          <el-table-column label="渠道代码" prop="channelCode" width="150">
                            <template #default="scope">
                              <el-tag type="success" effect="light">
                                {{ scope.row.channelCode }}
                              </el-tag>
                            </template>
                          </el-table-column>
                          <el-table-column label="状态" prop="status" width="100">
                            <template #default="scope">
                              <el-tag
                                :type="scope.row.status === 1 ? 'success' : 'danger'"
                                size="small"
                              >
                                {{ scope.row.status === 1 ? '启用' : '停用' }}
                              </el-tag>
                            </template>
                          </el-table-column>
                          <el-table-column label="备注" prop="remark" min-width="150" />
                          <el-table-column label="创建时间" prop="createTime" width="180">
                            <template #default="scope">
                              {{ scope.row.createTime ? formatDate(scope.row.createTime) : '-' }}
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                    </el-tab-pane>

                    <!-- 运营队伍权限 -->
                    <el-tab-pane
                      v-if="permissionPreview.bindingsByType?.OPERATION_TEAM?.length > 0"
                      label="运营队伍权限"
                      name="operation-team"
                    >
                      <div class="permission-table">
                        <el-table
                          :data="permissionPreview.bindingsByType.OPERATION_TEAM"
                          border
                          stripe
                          max-height="400"
                        >
                          <el-table-column label="运营队伍" prop="operationTeam" width="150">
                            <template #default="scope">
                              <el-tag type="warning" effect="light">
                                {{ scope.row.operationTeam }}
                              </el-tag>
                            </template>
                          </el-table-column>
                          <el-table-column label="目标部门" prop="targetDeptId" width="120">
                            <template #default="scope">
                              {{ getDeptName(scope.row.targetDeptId) }}
                            </template>
                          </el-table-column>
                          <el-table-column label="状态" prop="status" width="100">
                            <template #default="scope">
                              <el-tag
                                :type="scope.row.status === 1 ? 'success' : 'danger'"
                                size="small"
                              >
                                {{ scope.row.status === 1 ? '启用' : '停用' }}
                              </el-tag>
                            </template>
                          </el-table-column>
                          <el-table-column label="备注" prop="remark" min-width="150" />
                          <el-table-column label="创建时间" prop="createTime" width="180">
                            <template #default="scope">
                              {{ scope.row.createTime ? formatDate(scope.row.createTime) : '-' }}
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                    </el-tab-pane>

                    <!-- 区服权限 -->
                    <el-tab-pane
                      v-if="permissionPreview.bindingsByType?.SERVER?.length > 0"
                      label="区服权限"
                      name="server"
                    >
                      <div class="permission-table">
                        <el-table
                          :data="permissionPreview.bindingsByType.SERVER"
                          border
                          stripe
                          max-height="400"
                        >
                          <el-table-column label="产品名称" width="200">
                            <template #default="scope">
                              <el-tag type="primary" effect="light">
                                {{ getProductName(scope.row.productId) }}
                              </el-tag>
                            </template>
                          </el-table-column>
                          <el-table-column label="区服名称" prop="serverName" width="150">
                            <template #default="scope">
                              <el-tag type="danger" effect="light">
                                {{ scope.row.serverName }}
                              </el-tag>
                            </template>
                          </el-table-column>
                          <el-table-column label="渠道代码" prop="channelCode" width="150">
                            <template #default="scope">
                              <el-tag type="success" effect="light">
                                {{ scope.row.channelCode }}
                              </el-tag>
                            </template>
                          </el-table-column>
                          <el-table-column label="状态" prop="status" width="100">
                            <template #default="scope">
                              <el-tag
                                :type="scope.row.status === 1 ? 'success' : 'danger'"
                                size="small"
                              >
                                {{ scope.row.status === 1 ? '启用' : '停用' }}
                              </el-tag>
                            </template>
                          </el-table-column>
                          <el-table-column label="备注" prop="remark" min-width="150" />
                          <el-table-column label="创建时间" prop="createTime" width="180">
                            <template #default="scope">
                              {{ scope.row.createTime ? formatDate(scope.row.createTime) : '-' }}
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                    </el-tab-pane>

                    <!-- 调试信息Tab -->
                    <el-tab-pane label="调试信息" name="debug">
                      <div class="debug-panel">
                        <el-collapse>
                          <el-collapse-item title="权限统计概览" name="stats">
                            <div class="debug-stats">
                              <el-descriptions :column="2" border>
                                <el-descriptions-item label="权限绑定总数">
                                  {{ permissionPreview.totalBindings || 0 }}
                                </el-descriptions-item>
                                <el-descriptions-item label="产品权限数">
                                  {{ permissionPreview.productCount || 0 }}
                                </el-descriptions-item>
                                <el-descriptions-item label="渠道权限数">
                                  {{ permissionPreview.channelCount || 0 }}
                                </el-descriptions-item>
                                <el-descriptions-item label="运营队伍权限数">
                                  {{ permissionPreview.teamCount || 0 }}
                                </el-descriptions-item>
                                <el-descriptions-item label="区服权限数">
                                  {{ permissionPreview.serverCount || 0 }}
                                </el-descriptions-item>
                                <el-descriptions-item label="权限配置状态">
                                  <el-tag
                                    :type="permissionPreview.hasBindings ? 'success' : 'warning'"
                                  >
                                    {{ permissionPreview.hasBindings ? '已配置' : '未配置' }}
                                  </el-tag>
                                </el-descriptions-item>
                              </el-descriptions>
                            </div>
                          </el-collapse-item>

                          <el-collapse-item title="权限类型分组" name="groups">
                            <div class="debug-groups">
                              <el-tag type="primary" class="mr-2">
                                PRODUCT_CHANNEL:
                                {{ permissionPreview.bindingsByType?.PRODUCT_CHANNEL?.length || 0 }}
                                条
                              </el-tag>
                              <el-tag type="success" class="mr-2">
                                OPERATION_TEAM:
                                {{ permissionPreview.bindingsByType?.OPERATION_TEAM?.length || 0 }}
                                条
                              </el-tag>
                              <el-tag type="warning">
                                SERVER:
                                {{ permissionPreview.bindingsByType?.SERVER?.length || 0 }} 条
                              </el-tag>
                            </div>
                          </el-collapse-item>

                          <el-collapse-item title="原始数据" name="raw-data">
                            <div class="debug-raw">
                              <el-input
                                v-model="debugData"
                                type="textarea"
                                :rows="20"
                                readonly
                                placeholder="调试数据将显示在这里"
                              />
                            </div>
                          </el-collapse-item>
                        </el-collapse>
                      </div>
                    </el-tab-pane>
                  </el-tabs>
                </el-card>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-state-modern">
            <div class="empty-illustration">
              <el-icon size="80" color="#ddd"><View /></el-icon>
            </div>
            <h3 class="empty-title">请选择用户预览权限信息</h3>
            <p class="empty-description">选择用户后，您可以查看其详细的权限配置和访问范围</p>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">关闭</el-button>
        <el-button type="primary" @click="saveAllChanges" :loading="saving">
          保存所有更改
        </el-button>
      </div>
    </template>
  </Dialog>

  <!-- 资源绑定配置对话框 -->
  <el-dialog
    v-model="bindingDialogVisible"
    :title="editingBinding ? '编辑资源绑定' : '新增资源绑定'"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="binding-form">
      <el-form :model="bindingForm" :rules="bindingRules" ref="bindingFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="资源类型" prop="resourceType">
              <el-select
                v-model="bindingForm.resourceType"
                placeholder="请选择资源类型"
                @change="handleResourceTypeChange"
              >
                <el-option v-if="isOperationDept()" label="产品渠道" value="PRODUCT_CHANNEL" />
                <el-option v-if="isOpsDept()" label="运维资源" value="OPS_RESOURCE" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="bindingForm.status">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 产品渠道配置 -->
        <template v-if="bindingForm.resourceType === 'PRODUCT_CHANNEL'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="游戏产品" prop="productId">
                <el-select
                  v-model="bindingForm.productId"
                  placeholder="请选择游戏产品"
                  @change="handleBindingProductChange"
                >
                  <el-option
                    v-for="product in productList"
                    :key="product.id"
                    :label="product.productName"
                    :value="product.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="渠道" prop="channelCode">
                <el-select v-model="bindingForm.channelCode" placeholder="请选择渠道">
                  <el-option
                    v-for="channel in channelList"
                    :key="channel.channelCode"
                    :label="channel.channelName || channel.channelCode"
                    :value="channel.channelCode"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <!-- 运维资源配置（包含运营队伍和区服） -->
        <template v-if="bindingForm.resourceType === 'OPS_RESOURCE'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="目标部门" prop="targetDeptId">
                <el-select
                  v-model="bindingForm.targetDeptId"
                  placeholder="请选择目标部门"
                  @change="handleBindingTargetDeptChange"
                >
                  <el-option
                    v-for="dept in operationDeptList.filter((d) => d.id !== undefined)"
                    :key="dept.id"
                    :label="dept.deptName"
                    :value="dept.id!"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="运营队伍" prop="operationTeam">
                <el-select
                  v-model="bindingForm.targetDeptId"
                  placeholder="请选择运营队伍"
                  :disabled="!bindingForm.targetDeptId"
                >
                  <el-option
                    v-for="team in operationTeamList.filter((t) => t.deptName !== undefined)"
                    :key="team.id"
                    :label="team.deptName"
                    :value="team.deptName!"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="游戏产品" prop="productId">
                <el-select
                  v-model="bindingForm.productId"
                  placeholder="请选择游戏产品"
                  @change="handleBindingProductChange"
                >
                  <el-option
                    v-for="product in productList"
                    :key="product.id"
                    :label="product.productName"
                    :value="product.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="区服" prop="serverName">
                <el-select
                  v-model="bindingForm.serverName"
                  placeholder="请选择区服"
                  :disabled="!bindingForm.productId"
                >
                  <el-option
                    v-for="server in serverList"
                    :key="server"
                    :label="server"
                    :value="server"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <!-- 运营队伍配置 -->
        <template v-if="bindingForm.resourceType === 'OPERATION_TEAM'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="目标部门" prop="targetDeptId">
                <el-select
                  v-model="bindingForm.targetDeptId"
                  placeholder="请选择目标部门"
                  @change="handleBindingTargetDeptChange"
                >
                  <el-option
                    v-for="dept in operationDeptList.filter((d) => d.id !== undefined)"
                    :key="dept.id"
                    :label="dept.deptName"
                    :value="dept.id!"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="运营队伍" prop="operationTeam">
                <el-select
                  v-model="bindingForm.operationTeam"
                  placeholder="请选择运营队伍"
                  :disabled="!bindingForm.targetDeptId"
                >
                  <el-option
                    v-for="team in operationTeamList.filter((t) => t.deptName !== undefined)"
                    :key="team.id"
                    :label="team.deptName"
                    :value="team.deptName!"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="游戏产品" prop="productId">
                <el-select
                  v-model="bindingForm.productId"
                  placeholder="请选择游戏产品"
                  @change="handleBindingProductChange"
                >
                  <el-option
                    v-for="product in productList"
                    :key="product.id"
                    :label="product.productName"
                    :value="product.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="渠道" prop="channelCode">
                <el-select v-model="bindingForm.channelCode" placeholder="请选择渠道">
                  <el-option
                    v-for="channel in channelList"
                    :key="channel.channelCode"
                    :label="channel.channelName || channel.channelCode"
                    :value="channel.channelCode"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="区服" prop="serverName">
                <el-select
                  v-model="bindingForm.serverName"
                  placeholder="请选择区服"
                  :disabled="!bindingForm.productId"
                >
                  <el-option
                    v-for="server in serverList"
                    :key="server"
                    :label="server"
                    :value="server"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <!-- 区服配置 -->
        <template v-if="bindingForm.resourceType === 'SERVER'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="游戏产品" prop="productId">
                <el-select
                  v-model="bindingForm.productId"
                  placeholder="请选择游戏产品"
                  @change="handleBindingProductChange"
                >
                  <el-option
                    v-for="product in productList"
                    :key="product.id"
                    :label="product.productName"
                    :value="product.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="渠道" prop="channelCode">
                <el-select v-model="bindingForm.channelCode" placeholder="请选择渠道">
                  <el-option
                    v-for="channel in channelList"
                    :key="channel.channelCode"
                    :label="channel.channelName || channel.channelCode"
                    :value="channel.channelCode"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="区服" prop="serverName">
                <el-select
                  v-model="bindingForm.serverName"
                  placeholder="请选择区服"
                  :disabled="!bindingForm.productId"
                >
                  <el-option
                    v-for="server in serverList"
                    :key="server"
                    :label="server"
                    :value="server"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="bindingForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="bindingDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleBindingSave" :loading="bindingSaving">
          {{ editingBinding ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 批量操作对话框 -->
  <el-dialog
    v-model="batchDialogVisible"
    :title="batchOperationType === 'add' ? '批量新增资源绑定' : '批量删除资源绑定'"
    width="900px"
    :close-on-click-modal="false"
  >
    <div class="batch-form">
      <template v-if="batchOperationType === 'add'">
        <el-form :model="batchForm" :rules="batchRules" ref="batchFormRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="操作模式" prop="operationMode">
                <el-select v-model="batchForm.operationMode" placeholder="请选择操作模式">
                  <el-option label="增量添加（推荐）" value="ADD">
                    <span style="color: #67c23a">增量添加 - 保留现有绑定，添加新绑定</span>
                  </el-option>
                  <el-option label="完全替换" value="REPLACE">
                    <span style="color: #e6a23c">完全替换 - 删除所有现有绑定，重新设置</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="资源类型" prop="resourceType">
                <el-select
                  v-model="batchForm.resourceType"
                  placeholder="请选择资源类型"
                  @change="handleBatchResourceTypeChange"
                >
                  <el-option v-if="isOperationDept()" label="产品渠道" value="PRODUCT_CHANNEL" />
                  <el-option v-if="isOpsDept()" label="运维资源" value="OPS_RESOURCE" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-radio-group v-model="batchForm.status">
                  <el-radio :label="1">启用</el-radio>
                  <el-radio :label="0">停用</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 产品渠道批量配置 -->
          <template v-if="batchForm.resourceType === 'PRODUCT_CHANNEL'">
            <div class="resource-config-section">
              <div class="config-header">
                <el-alert
                  title="选择产品和渠道，点击添加按钮加入配置列表"
                  type="info"
                  :closable="false"
                  show-icon
                />
              </div>

              <div class="product-channel-selector">
                <el-card shadow="never" class="selector-card">
                  <template #header>
                    <div class="selector-header">
                      <Icon icon="ep:plus" />
                      <span>添加产品渠道组合</span>
                    </div>
                  </template>

                  <el-row :gutter="16" class="selector-row">
                    <el-col :span="8">
                      <el-form-item label="选择产品" class="selector-item">
                        <el-select
                          v-model="currentBatchProduct"
                          placeholder="请选择产品"
                          clearable
                          style="width: 100%"
                          @change="handleCurrentBatchProductChange"
                        >
                          <el-option
                            v-for="product in productList"
                            :key="product.id"
                            :label="product.productName"
                            :value="product.id"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="选择渠道" class="selector-item">
                        <el-select
                          v-model="currentBatchChannel"
                          placeholder="请选择渠道"
                          clearable
                          style="width: 100%"
                          :disabled="!currentBatchProduct"
                          :loading="channelLoading"
                        >
                          <el-option
                            v-for="channel in channelList"
                            :key="channel.channelCode"
                            :label="channel.channelName || channel.channelCode"
                            :value="channel.channelCode"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label=" " class="selector-item">
                        <el-button
                          type="primary"
                          size="large"
                          @click="addProductChannelCombination"
                          :disabled="!currentBatchProduct || !currentBatchChannel"
                          style="width: 100%"
                          class="add-combination-btn"
                        >
                          <Icon icon="ep:plus" class="mr-1" />
                          添加组合
                        </el-button>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-card>
              </div>

              <!-- 已添加的产品渠道组合 -->
              <div class="combinations-display">
                <div
                  v-if="batchForm.productChannelCombinations.length > 0"
                  class="combinations-list"
                >
                  <div class="combinations-header">
                    <div class="header-title">
                      <Icon icon="ep:document-checked" class="mr-1" />
                      <span>已添加的产品渠道组合</span>
                    </div>
                    <div class="header-actions">
                      <el-tag type="success" size="large">
                        <Icon icon="ep:check" class="mr-1" />
                        共 {{ batchForm.productChannelCombinations.length }} 个组合
                      </el-tag>
                      <el-button
                        type="danger"
                        text
                        size="small"
                        @click="clearAllProductChannelCombinations"
                        class="clear-btn"
                      >
                        <Icon icon="ep:delete" class="mr-1" />
                        清空所有
                      </el-button>
                    </div>
                  </div>

                  <div class="combinations-grid">
                    <div
                      v-for="(combo, index) in batchForm.productChannelCombinations"
                      :key="combo.productId + '-' + combo.channelCode"
                      class="combination-card"
                    >
                      <div class="combination-info">
                        <div class="combination-product">
                          <Icon icon="ep:box" class="product-icon" />
                          <span class="product-name">{{ getProductName(combo.productId) }}</span>
                        </div>
                        <div class="combination-separator">
                          <Icon icon="ep:right" />
                        </div>
                        <div class="combination-channel">
                          <Icon icon="ep:share" class="channel-icon" />
                          <span class="channel-name">{{ getChannelName(combo.channelCode) }}</span>
                        </div>
                      </div>
                      <el-button
                        type="danger"
                        text
                        size="small"
                        @click="removeProductChannelCombination(index)"
                        class="remove-btn"
                      >
                        <Icon icon="ep:delete" />
                      </el-button>
                    </div>
                  </div>
                </div>

                <!-- 空状态提示 -->
                <div v-else class="empty-combinations">
                  <el-empty description="暂无产品渠道组合" :image-size="120">
                    <template #image>
                      <Icon icon="ep:box" :size="80" color="#ddd" />
                    </template>
                    <template #description>
                      <div class="empty-description">
                        <p>暂无产品渠道组合</p>
                        <p class="empty-tip">请先选择产品和渠道，然后点击添加按钮</p>
                      </div>
                    </template>
                  </el-empty>
                </div>
              </div>
            </div>
          </template>

          <!-- 运维资源批量配置（组合添加模式） -->
          <template v-if="batchForm.resourceType === 'OPS_RESOURCE'">
            <div class="resource-config-section">
              <div class="config-header">
                <el-alert
                  title="选择目标部门、运营队伍、游戏产品、区服，点击添加按钮加入配置列表"
                  type="info"
                  :closable="false"
                  show-icon
                />
              </div>
              <el-card shadow="never" class="selector-card">
                <template #header>
                  <div class="selector-header">
                    <Icon icon="ep:plus" />
                    <span>添加运维资源组合</span>
                  </div>
                </template>
                <el-row :gutter="16" class="selector-row">
                  <el-col :span="6">
                    <el-form-item label="目标部门" class="selector-item">
                      <el-select
                        v-model="currentBatchOpsDept"
                        placeholder="请选择目标部门"
                        clearable
                        style="width: 100%"
                      >
                        <el-option
                          v-for="dept in operationDeptList.filter((d) => d.id !== undefined)"
                          :key="dept.id"
                          :label="dept.deptName"
                          :value="dept.id!"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="运营队伍" class="selector-item">
                      <el-select
                        v-model="currentBatchOpsTeam"
                        placeholder="请选择运营队伍"
                        clearable
                        style="width: 100%"
                      >
                        <el-option
                          v-for="team in getOpsTeamsForDept(currentBatchOpsDept)"
                          :key="team.id"
                          :label="team.deptName"
                          :value="team.deptName!"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="游戏产品" class="selector-item">
                      <el-select
                        v-model="currentBatchOpsProduct"
                        placeholder="请选择游戏产品"
                        clearable
                        style="width: 100%"
                      >
                        <el-option
                          v-for="product in productList"
                          :key="product.id"
                          :label="product.productName"
                          :value="product.id"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="区服" class="selector-item">
                      <el-select
                        v-model="currentBatchOpsServer"
                        placeholder="请选择区服"
                        clearable
                        style="width: 100%"
                        :disabled="!currentBatchOpsProduct"
                        @focus="
                          currentBatchOpsProduct && loadServersForProduct(currentBatchOpsProduct)
                        "
                      >
                        <el-option
                          v-for="server in serverList"
                          :key="server"
                          :label="server"
                          :value="server"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item label=" " class="selector-item">
                      <el-button
                        type="primary"
                        size="large"
                        @click="addOpsResourceCombination"
                        :disabled="
                          !currentBatchOpsDept ||
                          !currentBatchOpsTeam ||
                          !currentBatchOpsProduct ||
                          !currentBatchOpsServer
                        "
                        style="width: 100%"
                        class="add-combination-btn"
                      >
                        <Icon icon="ep:plus" class="mr-1" />
                        添加组合
                      </el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
              <!-- 已添加的运维资源组合 -->
              <div class="combinations-display">
                <div v-if="batchForm.opsResourceCombinations.length > 0" class="combinations-list">
                  <div class="combinations-header">
                    <div class="header-title">
                      <Icon icon="ep:document-checked" class="mr-1" />
                      <span>已添加的运维资源组合</span>
                    </div>
                    <div class="header-actions">
                      <el-tag type="success" size="large">
                        <Icon icon="ep:check" class="mr-1" />
                        共 {{ batchForm.opsResourceCombinations.length }} 个组合
                      </el-tag>
                      <el-button
                        type="danger"
                        text
                        size="small"
                        @click="clearAllOpsResourceCombinations"
                        class="clear-btn"
                      >
                        <Icon icon="ep:delete" class="mr-1" />
                        清空所有
                      </el-button>
                    </div>
                  </div>
                  <div class="combinations-grid">
                    <div
                      v-for="(combo, index) in batchForm.opsResourceCombinations"
                      :key="
                        combo.targetDeptId +
                        '-' +
                        combo.operationTeam +
                        '-' +
                        combo.productId +
                        '-' +
                        combo.serverName
                      "
                      class="combination-card"
                    >
                      <div class="combination-info">
                        <div class="combination-product">
                          <Icon icon="ep:box" class="product-icon" />
                          <span class="product-name">{{ getProductName(combo.productId) }}</span>
                        </div>
                        <div class="combination-separator">
                          <Icon icon="ep:right" />
                        </div>
                        <div class="combination-channel">
                          <Icon icon="ep:user-filled" class="channel-icon" />
                          <span class="channel-name">{{ combo.operationTeam }}</span>
                        </div>
                        <div class="combination-separator">
                          <Icon icon="ep:right" />
                        </div>
                        <div class="combination-server">
                          <Icon icon="ep:monitor" class="server-icon" />
                          <span class="server-name">{{ combo.serverName }}</span>
                        </div>
                        <div class="combination-separator">
                          <Icon icon="ep:right" />
                        </div>
                        <div class="combination-dept">
                          <Icon icon="ep:office-building" class="dept-icon" />
                          <span class="dept-name">{{ getDeptName(combo.targetDeptId) }}</span>
                        </div>
                      </div>
                      <el-button
                        type="danger"
                        text
                        size="small"
                        @click="removeOpsResourceCombination(index)"
                        class="remove-btn"
                      >
                        <Icon icon="ep:delete" />
                      </el-button>
                    </div>
                  </div>
                </div>
                <!-- 空状态提示 -->
                <div v-else class="empty-combinations">
                  <el-empty description="暂无运维资源组合" :image-size="120">
                    <template #image>
                      <Icon icon="ep:box" :size="80" color="#ddd" />
                    </template>
                    <template #description>
                      <div class="empty-description">
                        <p>暂无运维资源组合</p>
                        <p class="empty-tip"
                          >请先选择目标部门、运营队伍、游戏产品、区服，然后点击添加按钮</p
                        >
                      </div>
                    </template>
                  </el-empty>
                </div>
              </div>
            </div>
          </template>

          <!-- 运营队伍批量配置 -->
          <template v-if="batchForm.resourceType === 'OPERATION_TEAM'">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="目标部门" prop="targetDeptIds">
                  <el-select
                    v-model="batchForm.targetDeptIds"
                    multiple
                    placeholder="请选择目标部门"
                    @change="handleBatchTargetDeptChange"
                  >
                    <el-option
                      v-for="dept in operationDeptList.filter((d) => d.id !== undefined)"
                      :key="dept.id"
                      :label="dept.deptName"
                      :value="dept.id!"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="运营队伍" prop="operationTeams">
                  <el-select
                    v-model="batchForm.operationTeams"
                    multiple
                    placeholder="请选择运营队伍"
                  >
                    <el-option
                      v-for="team in operationTeamList.filter((t) => t.deptName !== undefined)"
                      :key="team.id"
                      :label="team.deptName"
                      :value="team.deptName!"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </template>

          <!-- 区服批量配置 -->
          <template v-if="batchForm.resourceType === 'SERVER'"></template>

          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="batchForm.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-form>

        <div class="batch-preview" v-if="batchPreviewList.length > 0">
          <el-divider content-position="left">
            <span style="font-size: 14px; color: #606266"
              >预览将要创建的绑定 ({{ batchPreviewList.length }} 条)</span
            >
          </el-divider>
          <div class="preview-list">
            <el-table :data="batchPreviewList" max-height="300" size="small">
              <el-table-column label="资源类型" prop="resourceType" width="120">
                <template #default="scope">
                  <el-tag :type="getResourceTypeTagType(scope.row.resourceType)" size="small">
                    {{ getResourceTypeText(scope.row.resourceType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="产品" prop="productName" width="120" />
              <el-table-column label="渠道" prop="channelCode" width="100" />
              <el-table-column label="运营队伍" prop="operationTeam" width="120" />
              <el-table-column label="区服" prop="serverName" width="100" />
              <el-table-column label="状态" prop="status" width="80">
                <template #default="scope">
                  <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="small">
                    {{ scope.row.status === 1 ? '启用' : '停用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="备注" prop="remark" min-width="150" />
            </el-table>
          </div>
        </div>
      </template>

      <template v-else>
        <el-alert title="批量删除确认" type="warning" :closable="false" show-icon>
          <template #default>
            <p>您确定要删除选中的 {{ selectedBindings.length }} 条资源绑定吗？此操作不可恢复。</p>
          </template>
        </el-alert>

        <div class="delete-preview mt-4">
          <el-table :data="selectedBindings" max-height="400" size="small">
            <el-table-column label="资源类型" prop="resourceType" width="120">
              <template #default="scope">
                <el-tag :type="getResourceTypeTagType(scope.row.resourceType)" size="small">
                  {{ getResourceTypeText(scope.row.resourceType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="产品" prop="productId" width="150">
              <template #default="scope">
                <span v-if="scope.row.productId">{{ getProductName(scope.row.productId) }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="渠道" prop="channelCode" width="100" />
            <el-table-column label="运营队伍" prop="operationTeam" width="120" />
            <el-table-column label="区服" prop="serverName" width="100" />
            <el-table-column label="状态" prop="status" width="80">
              <template #default="scope">
                <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="small">
                  {{ scope.row.status === 1 ? '启用' : '停用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="备注" prop="remark" min-width="150" />
          </el-table>
        </div>
      </template>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="batchDialogVisible = false">取消</el-button>
        <el-button
          :type="batchOperationType === 'add' ? 'primary' : 'danger'"
          @click="handleBatchSave"
          :loading="batchSaving"
        >
          {{ batchOperationType === 'add' ? '批量创建' : '确认删除' }}
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 角色分配对话框 -->
  <el-dialog
    v-model="roleAssignDialogVisible"
    :title="selectedDept?.deptLevel === 2 ? '选择小组角色' : '分配用户角色'"
    width="600px"
    :close-on-click-modal="false"
    class="role-assign-dialog"
  >
    <div class="role-assign-form">
      <el-alert
        v-if="selectedDept?.deptLevel === 2"
        :title="`为 ${selectedUsers.length} 个用户选择小组角色：组长负责管理，组员执行任务`"
        type="info"
        :closable="false"
        show-icon
        style="margin-bottom: 15px"
      />
      <el-alert
        v-else
        :title="`手动指定角色：将为 ${selectedUsers.length} 个用户分配指定角色（覆盖自动分配）`"
        type="warning"
        :closable="false"
        show-icon
        style="margin-bottom: 15px"
      />

      <el-alert
        v-if="selectedDept?.deptLevel !== 2"
        :title="`当前部门自动分配角色为：${getRoleText(getAutoRoleByDepartment())}`"
        type="info"
        :closable="false"
        show-icon
        style="margin-bottom: 20px"
      />

      <el-form
        :model="roleAssignForm"
        :rules="roleAssignRules"
        ref="roleAssignFormRef"
        label-width="120px"
      >
        <el-form-item label="选择角色" prop="role">
          <el-radio-group v-model="roleAssignForm.role" class="simple-role-group">
            <el-radio
              v-for="role in roleOptions"
              :key="role.code"
              :label="role.code"
              class="simple-role-radio"
            >
              <span class="role-label">{{ role.name }}</span>
              <el-tag :type="getRoleTagType(role.code)" size="small" class="role-tag">
                {{ role.code }}
              </el-tag>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="选中用户">
          <div class="selected-users-list">
            <el-tag
              v-for="user in selectedUsers"
              :key="user.id"
              type="primary"
              size="small"
              effect="light"
              style="margin-right: 8px; margin-bottom: 4px"
            >
              {{ user.username || user.nickname || `用户${user.id}` }}
            </el-tag>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="roleAssignDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleRoleAssign" :loading="roleAssignSaving">
          确定分配
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Delete,
  Edit,
  DocumentCopy,
  Refresh,
  Download,
  Monitor,
  PieChart,
  User,
  Connection,
  Box,
  Share,
  UserFilled,
  View,
  Warning
} from '@element-plus/icons-vue'
import { getUserPage, type UserVO } from '@/api/system/user'
import { ProductApi } from '@/api/erp/product/product'
import {
  DepartmentApi,
  type DepartmentVO,
  type UserDeptRelationVO,
  type UserResourceBindingVO,
  type UserResourceBindingReqVO,
  type OpsUserResourceBindingReqVO,
  type ProductChannelBindingItem,
  type ProductServerBindingItem,
  type BatchResourceBindingReqVO,
  type ResourceBindingItemVO
} from '@/api/erp/department'
import request from '@/config/axios'

defineOptions({ name: 'UserManageDialog' })

// 部门类型常量
const DEPT_TYPE = {
  OPERATION: 'OPERATION', // 运营部门
  OPS: 'OPS' // 运维部门
}

// 基础数据
const dialogVisible = ref(false)
const activeTab = ref('users')
const selectedDept = ref<DepartmentVO>()
const saving = ref(false)

// 用户分配相关
const assignedUsers = ref<UserDeptRelationVO[]>([])
const assignedLoading = ref(false)
const availableUsers = ref<UserVO[]>([])
const availableLoading = ref(false)
const selectedUsers = ref<UserVO[]>([])
const userSearchKeyword = ref('')

const userPageParams = reactive({
  pageNo: 1,
  pageSize: 50, // 增加页面大小到50
  username: undefined as string | undefined,
  nickname: undefined as string | undefined
})

// 添加分页总数和加载状态
const userPageTotal = ref(0)
const userPageLoading = ref(false)

// 角色分配相关
const roleAssignDialogVisible = ref(false)
const roleAssignSaving = ref(false)
const roleAssignFormRef = ref()
const roleAssignForm = reactive({
  role: ''
})

const roleAssignRules = {
  role: [{ required: true, message: '请选择角色', trigger: 'change' }]
}

// 角色选项定义
const allRoleOptions = [
  {
    code: 'SUPERVISOR',
    name: '主管',
    description: '负责部门整体管理，拥有最高权限'
  },
  {
    code: 'TEAM_LEADER',
    name: '组长',
    description: '负责小组管理和协调工作'
  },
  {
    code: 'TEAM_MEMBER',
    name: '组员',
    description: '普通成员，执行具体工作任务'
  }
]

// 根据当前部门层级过滤可用的角色选项
const roleOptions = computed(() => {
  if (!selectedDept.value?.deptLevel) {
    // 如果没有部门层级信息，默认显示小组的角色选项
    return allRoleOptions.filter((role) => role.code !== 'SUPERVISOR')
  }

  switch (selectedDept.value.deptLevel) {
    case 1: // 主管部门，只能选择主管角色
      return allRoleOptions.filter((role) => role.code === 'SUPERVISOR')
    case 2: // 小组，可以选择组长或组员角色
      return allRoleOptions.filter((role) => role.code !== 'SUPERVISOR')
    default:
      // 未知层级，默认显示小组的角色选项
      return allRoleOptions.filter((role) => role.code !== 'SUPERVISOR')
  }
})
const selectedUserId = ref<number>()
const userResourceBindings = ref<UserResourceBindingVO[]>([])
const selectedBindings = ref<UserResourceBindingVO[]>([])
const bindingLoading = ref(false)
const bindingDialogVisible = ref(false)
const editingBinding = ref<UserResourceBindingVO | null>(null)
const batchDialogVisible = ref(false)
const batchOperationType = ref<'add' | 'delete'>('add')

// 新增表单数据
const bindingFormRef = ref()
const bindingForm = reactive({
  resourceType: '',
  productId: 0,
  channelCode: '',
  targetDeptId: 0,
  operationTeam: '',
  serverName: '',
  status: 1,
  remark: ''
})

const bindingRules = {
  resourceType: [{ required: true, message: '请选择资源类型', trigger: 'change' }],
  productId: [{ required: true, message: '请选择游戏产品', trigger: 'change' }],
  channelCode: [{ required: true, message: '请选择渠道', trigger: 'change' }],
  targetDeptId: [{ required: true, message: '请选择目标部门', trigger: 'change' }],
  operationTeam: [{ required: true, message: '请选择运营队伍', trigger: 'change' }],
  serverName: [{ required: true, message: '请选择区服', trigger: 'change' }]
}

const bindingSaving = ref(false)

// 批量表单数据
const batchFormRef = ref()
const batchForm = reactive({
  operationMode: 'ADD', // 操作模式，默认为增量添加
  resourceType: '',
  productIds: [] as number[],
  channelCodes: [] as string[],
  productChannelCombinations: [] as Array<{ productId: number; channelCode: string }>,
  targetDeptIds: [] as number[],
  operationTeams: [] as string[],
  serverNames: [] as string[],
  opsResourceCombinations: [] as Array<{
    targetDeptId: number
    operationTeam: string
    productId: number
    serverName: string
  }>,
  status: 1,
  remark: ''
})

const batchRules = {
  resourceType: [{ required: true, message: '请选择资源类型', trigger: 'change' }],
  productIds: [{ required: true, message: '请选择游戏产品', trigger: 'change' }],
  channelCodes: [{ required: true, message: '请选择渠道', trigger: 'change' }],
  targetDeptIds: [{ required: true, message: '请选择目标部门', trigger: 'change' }],
  operationTeams: [{ required: true, message: '请选择运营队伍', trigger: 'change' }],
  serverNames: [{ required: true, message: '请选择区服', trigger: 'change' }]
}

const batchSaving = ref(false)
const batchPreviewList = ref<any[]>([])

// 权限预览相关
const previewUserId = ref<number>()
const permissionPreview = ref<any>(null)
const activePermissionTab = ref('summary')
const debugData = computed(() => {
  return JSON.stringify(permissionPreview.value, null, 2)
})

// 资源数据
const productList = ref<any[]>([])
const channelList = ref<any[]>([])
const serverList = ref<string[]>([])
const operationDeptList = ref<DepartmentVO[]>([])
const operationTeamList = ref<DepartmentVO[]>([])

// 绑定状态
const bindingStatus = ref(1)
const remark = ref('')

// 其他数据
const targetDeptId = ref<number>()
const selectedProductIds = ref<number[]>([])
const selectedChannelCodes = ref<string[]>([])
const selectedTeams = ref<string[]>([])
const selectedServerNames = ref<string[]>([])

// 修复 getDeptName 函数中的 deptList 引用
const deptList = ref<DepartmentVO[]>([])

// 当前选择的批量配置值
const currentBatchProduct = ref<number | undefined>(undefined)
const currentBatchChannel = ref<string | undefined>(undefined)
const channelLoading = ref(false)

const currentBatchOpsDept = ref<number | undefined>(undefined)
const currentBatchOpsTeam = ref<string | undefined>(undefined)
const currentBatchOpsProduct = ref<number | undefined>(undefined)
const currentBatchOpsServer = ref<string | undefined>(undefined)

/** 打开弹窗 */
const open = async (dept: DepartmentVO) => {
  selectedDept.value = dept
  dialogVisible.value = true
  activeTab.value = 'users'

  // 重置状态
  resetConfig()

  // 并行加载数据
  await Promise.all([
    loadAssignedUsers(),
    loadAvailableUsers(),
    loadProducts(),
    loadOperationDepts()
  ])
}

/** 关闭弹窗 */
const cancel = () => {
  dialogVisible.value = false
  resetConfig()
}

/** 重置配置 */
const resetConfig = () => {
  selectedUserId.value = undefined
  previewUserId.value = undefined
  permissionPreview.value = null
  selectedBindings.value = []
  userResourceBindings.value = []
  editingBinding.value = null

  // 重置表单数据
  targetDeptId.value = undefined
  selectedProductIds.value = []
  selectedChannelCodes.value = []
  selectedTeams.value = []
  selectedServerNames.value = []
  bindingStatus.value = 1
  remark.value = ''

  // 重置权限预览
  activePermissionTab.value = 'summary'
}

/** 保存配置 */
const saveConfig = async () => {
  if (!selectedUserId.value || !selectedDept.value?.id) {
    ElMessage.warning('请先选择用户')
    return
  }

  if (isOperationDept()) {
    // 运营部门配置验证
    if (selectedProductIds.value.length === 0) {
      ElMessage.warning('请选择游戏产品')
      return
    }
    if (selectedChannelCodes.value.length === 0) {
      ElMessage.warning('请选择渠道')
      return
    }

    try {
      // 调用API保存运营部门配置
      const configData: UserResourceBindingVO = {
        userId: selectedUserId.value,
        deptId: selectedDept.value.id,
        productIds: selectedProductIds.value,
        channelCodes: selectedChannelCodes.value,
        status: bindingStatus.value,
        remark: remark.value
      }

      await DepartmentApi.saveUserResourceBinding(configData)
      ElMessage.success('运营部门配置保存成功')
    } catch (error) {
      console.error('保存运营部门配置失败:', error)
      ElMessage.error('保存失败，请重试')
    }
  } else if (isOpsDept()) {
    // 运维部门配置验证
    if (!targetDeptId.value) {
      ElMessage.warning('请选择目标运营部门')
      return
    }
    if (selectedTeams.value.length === 0) {
      ElMessage.warning('请选择运营队伍')
      return
    }
    if (selectedProductIds.value.length === 0) {
      ElMessage.warning('请选择游戏产品')
      return
    }
    if (selectedServerNames.value.length === 0) {
      ElMessage.warning('请选择区服')
      return
    }

    try {
      // 调用API保存运维部门配置
      const configData: UserResourceBindingVO = {
        userId: selectedUserId.value,
        deptId: selectedDept.value.id,
        targetDeptId: targetDeptId.value,
        teams: selectedTeams.value,
        productIds: selectedProductIds.value,
        serverNames: selectedServerNames.value,
        status: bindingStatus.value,
        remark: remark.value
      }

      await DepartmentApi.saveOpsUserResourceBinding(configData)
      ElMessage.success('运维部门配置保存成功')
    } catch (error) {
      console.error('保存运维部门配置失败:', error)
      ElMessage.error('保存失败，请重试')
    }
  }
}

/** 加载已分配用户 */
const loadAssignedUsers = async () => {
  if (!selectedDept.value?.id) return

  assignedLoading.value = true
  try {
    const data = await DepartmentApi.getDepartmentUsers(selectedDept.value.id)
    assignedUsers.value = data || []
  } catch (error) {
    console.error('加载已分配用户失败:', error)
    ElMessage.error('加载已分配用户失败')
  } finally {
    assignedLoading.value = false
  }
}

/** 加载可用用户 */
const loadAvailableUsers = async () => {
  availableLoading.value = true
  userPageLoading.value = true
  try {
    const data = await getUserPage(userPageParams)
    availableUsers.value = data.list || []
    userPageTotal.value = data.total || 0
  } catch (error) {
    console.error('加载可用用户失败:', error)
    ElMessage.error('加载可用用户失败')
  } finally {
    availableLoading.value = false
    userPageLoading.value = false
  }
}

/** 搜索用户 */
const searchUsers = () => {
  userPageParams.username = userSearchKeyword.value
  userPageParams.nickname = userSearchKeyword.value
  userPageParams.pageNo = 1
  loadAvailableUsers()
}

/** 选择用户 */
const handleSelectionChange = (selection: UserVO[]) => {
  selectedUsers.value = selection
}

/** 批量分配用户 */
const batchAssignUsers = async () => {
  if (!selectedDept.value?.id || selectedUsers.value.length === 0) return

  const relationType = getCurrentRelationType()
  const relationTypeText = getRelationTypeText(relationType)

  // 对于小组（第2层），打开角色选择对话框
  if (selectedDept.value.deptLevel === 2) {
    openRoleAssignDialog()
    return
  }

  // 对于主管部门（第1层），自动分配主管角色
  const autoRole = getAutoRoleByDepartment()

  try {
    const promises = selectedUsers.value.map((user) =>
      DepartmentApi.assignUserToDepartment({
        userId: user.id,
        deptIds: [selectedDept.value!.id!],
        relationType: relationType,
        role: autoRole // 自动分配角色
      })
    )

    await Promise.all(promises)
    ElMessage.success(`用户分配成功，关系类型：${relationTypeText}，角色：${getRoleText(autoRole)}`)
    await loadAssignedUsers()
    selectedUsers.value = []
  } catch (error) {
    console.error('分配用户失败:', error)
    ElMessage.error('分配用户失败')
  }
}

/** 移除用户 */
const removeUser = async (userId: number) => {
  if (!selectedDept.value?.id) return

  try {
    // 先检查用户是否有权限绑定
    const userBindings = await DepartmentApi.getUserResourceBindings(userId)
    const hasBindings = userBindings && userBindings.length > 0

    let confirmMessage = '确认移除该用户吗？'
    let confirmTitle = '移除用户确认'

    if (hasBindings) {
      const bindingCount = userBindings.length

      // 分类统计权限绑定
      const productChannelBindings = userBindings.filter(
        (b) => b.resourceType === 'PRODUCT_CHANNEL'
      )
      const operationTeamBindings = userBindings.filter((b) => b.resourceType === 'OPERATION_TEAM')
      const serverBindings = userBindings.filter((b) => b.resourceType === 'SERVER')

      let bindingDetails = '权限绑定详情：\n'
      if (productChannelBindings.length > 0) {
        bindingDetails += `• 产品渠道权限：${productChannelBindings.length} 条\n`
      }
      if (operationTeamBindings.length > 0) {
        bindingDetails += `• 运营队伍权限：${operationTeamBindings.length} 条\n`
      }
      if (serverBindings.length > 0) {
        bindingDetails += `• 区服权限：${serverBindings.length} 条\n`
      }

      confirmMessage = `该用户当前有 ${bindingCount} 条资源权限绑定：\n\n${bindingDetails}\n移除用户将同时删除：\n✓ 用户部门关系\n✓ 所有资源权限绑定\n\n⚠️ 此操作不可恢复，确认继续吗？`
      confirmTitle = '⚠️ 移除用户及权限确认'
    }

    await ElMessageBox.confirm(confirmMessage, confirmTitle, {
      confirmButtonText: hasBindings ? '确认移除用户及权限' : '确认移除',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: false,
      showClose: true,
      customClass: 'remove-user-confirm-dialog'
    })

    // 如果有权限绑定，先删除所有权限绑定
    if (hasBindings) {
      const deletePromises = userBindings
        .filter((binding) => binding.id)
        .map((binding) => DepartmentApi.deleteUserResourceBinding(binding.id!))

      await Promise.all(deletePromises)
      ElMessage.success(`已删除 ${userBindings.length} 条权限绑定`)
    }

    // 然后删除用户部门关系
    await DepartmentApi.removeUserFromDepartment(userId, selectedDept.value.id)

    if (hasBindings) {
      ElMessage.success('用户移除成功，已同时清理相关权限绑定')
    } else {
      ElMessage.success('用户移除成功')
    }

    await loadAssignedUsers()
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消操作，不显示错误信息
      return
    }
    console.error('移除用户失败:', error)
    ElMessage.error('移除用户失败，请重试')
  }
}

/** 处理资源绑定选择变化 */
const handleBindingSelectionChange = (selection: UserResourceBindingVO[]) => {
  selectedBindings.value = selection
}

/** 判断是否运营部门 */
const isOperationDept = () => {
  return selectedDept.value?.deptName?.includes('运营') || false
}

/** 判断是否运维部门 */
const isOpsDept = () => {
  return selectedDept.value?.deptName?.includes('运维') || false
}

/** 获取部门类型文本 */
const getDeptTypeText = () => {
  if (isOpsDept()) return '运维部门'
  if (isOperationDept()) return '运营部门'
  return '其他部门'
}

/** 加载运营部门列表 */
const loadOperationDepts = async () => {
  try {
    const data = await DepartmentApi.getDepartmentTree()
    deptList.value = data || []
    operationDeptList.value = data?.filter((dept) => dept.deptName?.includes('运营')) || []
  } catch (error) {
    console.error('加载运营部门列表失败:', error)
    ElMessage.error('加载运营部门列表失败')
  }
}

/** 在部门树中查找指定部门 */
const findDeptInTree = (deptList: DepartmentVO[], targetId: number): DepartmentVO | null => {
  for (const dept of deptList) {
    if (dept.id === targetId) {
      return dept
    }
    if (dept.children && dept.children.length > 0) {
      const found = findDeptInTree(dept.children, targetId)
      if (found) return found
    }
  }
  return null
}

/** 加载游戏产品列表 */
const loadProducts = async () => {
  try {
    const data = await ProductApi.getProductPage({ pageNo: 1, pageSize: 100 })
    productList.value = data.list || []
  } catch (error) {
    console.error('加载产品列表失败:', error)
    ElMessage.error('加载产品列表失败')
  }
}

/** 加载运营队伍列表 */
const loadOperationTeams = async () => {
  try {
    if (targetDeptId.value) {
      const deptTree = await DepartmentApi.getDepartmentTree()
      const targetDept = findDeptInTree(deptTree, targetDeptId.value)

      if (targetDept && targetDept.children && targetDept.children.length > 0) {
        operationTeamList.value = targetDept.children
      } else {
        operationTeamList.value = []
        ElMessage.warning('该部门下暂无子部门')
      }
    } else {
      operationTeamList.value = []
    }
  } catch (error) {
    console.error('加载运营队伍列表失败:', error)
    operationTeamList.value = []
  }
}

/** 目标运营部门变更处理 */
const handleTargetDeptChange = async () => {
  selectedTeams.value = []
  await loadOperationTeams()
}

/** 绑定表单中目标部门变更处理 */
const handleBindingTargetDeptChange = async () => {
  // 清空运营队伍选择
  bindingForm.operationTeam = ''

  // 加载选中目标部门下的运营队伍（子部门）
  await loadBindingOperationTeams()
}

/** 加载绑定表单的运营队伍列表 */
const loadBindingOperationTeams = async () => {
  try {
    if (bindingForm.targetDeptId && bindingForm.targetDeptId > 0) {
      const deptTree = await DepartmentApi.getDepartmentTree()
      const targetDept = findDeptInTree(deptTree, bindingForm.targetDeptId)

      if (targetDept && targetDept.children && targetDept.children.length > 0) {
        operationTeamList.value = targetDept.children
      } else {
        operationTeamList.value = []
        ElMessage.warning('该部门下暂无运营队伍')
      }
    } else {
      operationTeamList.value = []
    }
  } catch (error) {
    console.error('加载运营队伍列表失败:', error)
    operationTeamList.value = []
  }
}

/** 产品变更处理 */
const handleBindingProductChange = async () => {
  // 清空区服选择
  bindingForm.serverName = ''

  // 如果有选择产品，则加载对应的区服列表
  if (bindingForm.productId) {
    await loadServersForProduct(bindingForm.productId)
  } else {
    // 如果没有选择产品，清空区服列表
    serverList.value = []
  }
}

/** 为指定产品加载渠道列表 */
const loadChannelsForProduct = async (productId: number) => {
  try {
    console.log('为产品加载渠道:', productId)
    const data = await request.get({
      url: '/erp/channel/list',
      params: {
        productId: productId
      }
    })
    console.log('渠道API返回数据:', data)
    channelList.value = (data.list || data || []).map((item: any) => ({
      channelCode: item.channelCode,
      channelName: item.channelName || item.channelCode
    }))
    console.log('设置渠道列表:', channelList.value)
  } catch (error) {
    console.error('加载渠道列表失败:', error)
    // 降级使用模拟数据
    channelList.value = [
      { channelCode: 'c1', channelName: '官方渠道' },
      { channelCode: 'c2', channelName: '应用宝' },
      { channelCode: 'c3', channelName: '华为' },
      { channelCode: 'c4', channelName: '小米' }
    ]
    console.log('使用模拟渠道数据:', channelList.value)
  }
}

/** 为指定产品加载区服列表 */
const loadServersForProduct = async (productId: number) => {
  try {
    console.log('为产品加载区服:', productId)
    const data = await request.post({
      url: '/erp/order/GetServer',
      data: [productId]
    })
    console.log('区服API返回数据:', data)
    serverList.value = ['全部', ...(data || [])]
    console.log('设置区服列表:', serverList.value)
  } catch (error) {
    console.error('加载区服列表失败:', error)
    // 降级使用模拟数据
    serverList.value = ['全部', '1服', '2服', '3服', '4服', '5服']
    console.log('使用模拟区服数据:', serverList.value)
  }
}

/** 产品变更处理 */
const handleProductChange = async () => {
  // 重置相关选择
  if (isOperationDept()) {
    selectedChannelCodes.value = []
    await loadChannels()
  } else if (isOpsDept()) {
    selectedServerNames.value = []
    await loadServers()
  }
}

/** 加载渠道列表 */
const loadChannels = async () => {
  try {
    if (selectedProductIds.value.length > 0) {
      const data = await request.get({
        url: '/erp/channel/list',
        params: {
          productId: selectedProductIds.value
        }
      })
      channelList.value = (data.list || []).map((item) => ({
        channelCode: item.channelCode,
        channelName: item.channelName || item.channelCode
      }))
    } else {
      channelList.value = []
    }
  } catch (error) {
    console.error('加载渠道列表失败:', error)
    // 降级使用模拟数据
    if (selectedProductIds.value.length > 0) {
      channelList.value = [
        { channelCode: 'c1', channelName: '官方渠道' },
        { channelCode: 'c2', channelName: '应用宝' },
        { channelCode: 'c3', channelName: '华为' },
        { channelCode: 'c4', channelName: '小米' }
      ]
    } else {
      channelList.value = []
    }
  }
}

/** 加载区服列表 */
const loadServers = async () => {
  try {
    if (selectedProductIds.value.length > 0) {
      const data = await request.post({
        url: '/erp/order/GetServer',
        data: selectedProductIds.value
      })
      serverList.value = ['全部', ...(data || [])]
    } else {
      serverList.value = []
    }
  } catch (error) {
    console.error('加载区服列表失败:', error)
    // 降级使用模拟数据
    if (selectedProductIds.value.length > 0) {
      serverList.value = ['全部', '1服', '2服', '3服', '4服', '5服']
    } else {
      serverList.value = []
    }
  }
}

/** 资源用户变更处理 */
const handleResourceUserChange = async () => {
  console.log('handleResourceUserChange被调用，selectedUserId:', selectedUserId.value)

  // 只重置相关的绑定数据，不重置selectedUserId
  selectedBindings.value = []
  userResourceBindings.value = []
  editingBinding.value = null

  if (selectedUserId.value) {
    console.log('开始加载用户资源绑定')
    await refreshBindings()
  }
}

/** 获取选中用户名称 */
const getSelectedUserName = () => {
  const user = assignedUsers.value.find((u) => u.userId === selectedUserId.value)
  return user?.userName || user?.username || `用户${user?.userId || ''}`
}

/** 获取预览用户名称 */
const getPreviewUserName = () => {
  const user = assignedUsers.value.find((u) => u.userId === previewUserId.value)
  return user?.userName || user?.username || `用户${user?.userId || ''}`
}

/** 获取产品名称 */
const getProductName = (productId: number) => {
  const product = productList.value.find((p) => p.id === productId)
  return product ? product.productName : `产品${productId}`
}

/** 获取渠道名称 */
const getChannelName = (channelCode: string) => {
  const channel = channelList.value.find((c) => c.channelCode === channelCode)
  return channel ? channel.channelName : channelCode
}

/** 获取部门名称 */
const getDeptName = (deptId: number) => {
  const dept = deptList.value.find((d) => d.id === deptId)
  return dept ? dept.deptName : `部门${deptId}`
}

/** 加载用户权限预览 */
const loadUserPermissionPreview = async () => {
  if (!previewUserId.value) {
    permissionPreview.value = null
    return
  }

  try {
    const [
      userBindings,
      accessibleBindings,
      boundProductIds,
      boundChannelCodes,
      boundOperationTeams,
      boundServerNames
    ] = await Promise.all([
      DepartmentApi.getUserResourceBindings(previewUserId.value),
      DepartmentApi.getAccessibleBindings(previewUserId.value),
      DepartmentApi.getUserBoundProductIds(previewUserId.value),
      DepartmentApi.getUserBoundChannelCodes(previewUserId.value),
      DepartmentApi.getUserBoundOperationTeams(previewUserId.value),
      DepartmentApi.getUserBoundServerNames(previewUserId.value)
    ])

    permissionPreview.value = {
      hasBindings: userBindings.length > 0,
      totalBindings: userBindings.length,
      productCount: boundProductIds.length,
      channelCount: boundChannelCodes.length,
      teamCount: boundOperationTeams.length,
      serverCount: boundServerNames.length,
      userBindings: userBindings || [],
      accessibleBindings: accessibleBindings || [],
      boundProductIds: boundProductIds || [],
      boundChannelCodes: boundChannelCodes || [],
      boundOperationTeams: boundOperationTeams || [],
      boundServerNames: boundServerNames || [],
      bindingsByType: {
        PRODUCT_CHANNEL: userBindings.filter((b) => b.resourceType === 'PRODUCT_CHANNEL'),
        OPERATION_TEAM: userBindings.filter((b) => b.resourceType === 'OPERATION_TEAM'),
        SERVER: userBindings.filter((b) => b.resourceType === 'SERVER')
      }
    }
  } catch (error) {
    console.error('加载用户权限预览失败:', error)
    ElMessage.error('加载权限信息失败')

    permissionPreview.value = {
      hasBindings: false,
      totalBindings: 0,
      productCount: 0,
      channelCount: 0,
      teamCount: 0,
      serverCount: 0,
      error: '权限信息加载失败'
    }
  }
}

/** 打开角色分配对话框 */
const openRoleAssignDialog = () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请先选择用户')
    return
  }

  // 重置角色选择，根据部门层级设置默认值
  roleAssignForm.role = ''

  // 强制刷新计算属性
  nextTick(() => {
    // 如果是小组（第2层），默认选择组员角色
    if (selectedDept.value?.deptLevel === 2) {
      roleAssignForm.role = 'TEAM_MEMBER'
    } else if (selectedDept.value?.deptLevel === 1) {
      // 如果是主管部门（第1层），默认选择主管角色
      roleAssignForm.role = 'SUPERVISOR'
    }
  })

  roleAssignDialogVisible.value = true
}

/** 处理角色分配 */
const handleRoleAssign = async () => {
  if (!roleAssignFormRef.value) return

  try {
    await roleAssignFormRef.value.validate()

    if (!selectedDept.value?.id || selectedUsers.value.length === 0) {
      ElMessage.warning('请先选择部门和用户')
      return
    }

    roleAssignSaving.value = true

    // 为每个选中的用户分配指定角色（手动分配，覆盖自动分配的角色）
    const promises = selectedUsers.value.map((user) =>
      DepartmentApi.assignUserToDepartment({
        userId: user.id,
        deptIds: [selectedDept.value!.id!],
        relationType: getCurrentRelationType(),
        role: roleAssignForm.role // 使用手动选择的角色
      })
    )

    await Promise.all(promises)

    const roleName =
      allRoleOptions.find((r) => r.code === roleAssignForm.role)?.name || roleAssignForm.role
    ElMessage.success(`成功为 ${selectedUsers.value.length} 个用户手动分配${roleName}角色`)

    await loadAssignedUsers()
    selectedUsers.value = []
    roleAssignDialogVisible.value = false
  } catch (error) {
    if (error !== false) {
      // 不是表单验证错误
      console.error('分配角色失败:', error)
      ElMessage.error('分配角色失败，请重试')
    }
  } finally {
    roleAssignSaving.value = false
  }
}

/** 获取角色标签类型 */
const getRoleTagType = (role: string) => {
  switch (role) {
    case 'SUPERVISOR':
      return 'danger' // 主管 - 红色
    case 'TEAM_LEADER':
      return 'warning' // 组长 - 橙色
    case 'TEAM_MEMBER':
      return 'success' // 组员 - 绿色
    default:
      return 'info'
  }
}

/** 获取角色文本 */
const getRoleText = (role: string) => {
  switch (role) {
    case 'SUPERVISOR':
      return '主管'
    case 'TEAM_LEADER':
      return '组长'
    case 'TEAM_MEMBER':
      return '组员'
    default:
      return role || '未设置'
  }
}

/** 获取关系类型标签类型 */
const getRelationTypeTagType = (type: number) => {
  switch (type) {
    case 1:
      return 'danger' // 主管
    case 2:
      return 'warning' // 组长
    case 3:
      return 'success' // 组员
    default:
      return 'info'
  }
}

/** 获取关系类型文本 */
const getRelationTypeText = (type: number) => {
  switch (type) {
    case 1:
      return '主管'
    case 2:
      return '组长'
    case 3:
      return '组员'
    default:
      return '未知'
  }
}

/** 打开用户选择器 */
const openUserSelector = () => {
  console.log('打开用户选择器')
}

// 监听预览用户变化
watch(previewUserId, (newVal) => {
  if (newVal) {
    loadUserPermissionPreview()
  }
})

/** 添加新资源绑定 */
const addNewBinding = async () => {
  editingBinding.value = null
  resetBindingForm()

  // 根据部门类型设置默认资源类型
  if (isOperationDept()) {
    bindingForm.resourceType = 'PRODUCT_CHANNEL'
  } else if (isOpsDept()) {
    bindingForm.resourceType = 'OPS_RESOURCE'
  }

  bindingDialogVisible.value = true
}

/** 编辑资源绑定 */
const editBinding = async (binding: UserResourceBindingVO) => {
  editingBinding.value = binding
  resetBindingForm()

  // 填充表单数据
  bindingForm.resourceType = binding.resourceType || ''
  bindingForm.productId = binding.productId || 0
  bindingForm.channelCode = binding.channelCode || ''
  bindingForm.targetDeptId = binding.targetDeptId || 0
  bindingForm.operationTeam = binding.operationTeam || ''

  // 如果是运营队伍或运维资源类型，且有目标部门，则加载运营队伍列表
  if (
    (bindingForm.resourceType === 'OPERATION_TEAM' ||
      bindingForm.resourceType === 'OPS_RESOURCE') &&
    bindingForm.targetDeptId > 0
  ) {
    await loadBindingOperationTeams()
  }
  bindingForm.serverName = binding.serverName || ''
  bindingForm.status = binding.status
  bindingForm.remark = binding.remark || ''

  // 根据资源类型加载对应的数据
  if (
    bindingForm.resourceType === 'PRODUCT_CHANNEL' ||
    bindingForm.resourceType === 'SERVER' ||
    bindingForm.resourceType === 'OPS_RESOURCE'
  ) {
    channelList.value = []
    serverList.value = []
  }
  if (
    bindingForm.resourceType === 'OPERATION_TEAM' ||
    bindingForm.resourceType === 'OPS_RESOURCE'
  ) {
    operationTeamList.value = []
  }

  bindingDialogVisible.value = true
}

/** 重置绑定表单 */
const resetBindingForm = () => {
  bindingForm.resourceType = ''
  bindingForm.productId = 0
  bindingForm.channelCode = ''
  bindingForm.targetDeptId = 0
  bindingForm.operationTeam = ''
  bindingForm.serverName = ''
  bindingForm.status = 1
  bindingForm.remark = ''
}

/** 资源类型变更处理 */
const handleResourceTypeChange = async () => {
  // 清空相关字段
  bindingForm.productId = 0
  bindingForm.channelCode = ''
  bindingForm.targetDeptId = 0
  bindingForm.operationTeam = ''
  bindingForm.serverName = ''

  // 根据资源类型加载对应的数据
  if (
    bindingForm.resourceType === 'PRODUCT_CHANNEL' ||
    bindingForm.resourceType === 'SERVER' ||
    bindingForm.resourceType === 'OPS_RESOURCE'
  ) {
    channelList.value = []
    serverList.value = []
  }
  if (
    bindingForm.resourceType === 'OPERATION_TEAM' ||
    bindingForm.resourceType === 'OPS_RESOURCE'
  ) {
    operationTeamList.value = []
  }
}

/** 绑定表单保存 */
const handleBindingSave = async () => {
  if (!bindingFormRef.value) return

  try {
    await bindingFormRef.value.validate()

    if (!selectedUserId.value || !selectedDept.value?.id) {
      ElMessage.warning('请先选择用户')
      return
    }

    bindingSaving.value = true

    const bindingData: UserResourceBindingReqVO = {
      userId: selectedUserId.value,
      deptId: selectedDept.value.id,
      resourceType: bindingForm.resourceType,
      status: bindingForm.status,
      remark: bindingForm.remark
    }

    // 根据资源类型设置相应字段
    if (bindingForm.resourceType === 'PRODUCT_CHANNEL') {
      bindingData.productIds = bindingForm.productId > 0 ? [bindingForm.productId] : []
      bindingData.channelCodes = bindingForm.channelCode ? [bindingForm.channelCode] : []
    } else if (bindingForm.resourceType === 'OPERATION_TEAM') {
      bindingData.targetDeptId = bindingForm.targetDeptId > 0 ? bindingForm.targetDeptId : undefined
      bindingData.teams = bindingForm.operationTeam ? [bindingForm.operationTeam] : []
    } else if (bindingForm.resourceType === 'SERVER') {
      bindingData.productIds = bindingForm.productId > 0 ? [bindingForm.productId] : []
      bindingData.channelCodes = bindingForm.channelCode ? [bindingForm.channelCode] : []
      bindingData.serverNames = bindingForm.serverName ? [bindingForm.serverName] : []
    } else if (bindingForm.resourceType === 'OPS_RESOURCE') {
      // 运维资源类型，包含运营队伍和区服信息
      bindingData.targetDeptId = bindingForm.targetDeptId > 0 ? bindingForm.targetDeptId : undefined
      bindingData.teams = bindingForm.operationTeam ? [bindingForm.operationTeam] : []
      bindingData.productIds = bindingForm.productId > 0 ? [bindingForm.productId] : []
      bindingData.channelCodes = bindingForm.channelCode ? [bindingForm.channelCode] : []
      bindingData.serverNames = bindingForm.serverName ? [bindingForm.serverName] : []
    }

    // 根据部门类型选择不同的API端点
    if (editingBinding.value?.id) {
      // 更新现有绑定
      if (isOpsDept()) {
        await DepartmentApi.saveOpsUserResourceBinding(bindingData)
      } else {
        await DepartmentApi.saveUserResourceBinding(bindingData)
      }
      ElMessage.success('资源绑定更新成功')
    } else {
      // 创建新绑定 - 根据部门类型使用不同的API
      if (isOpsDept()) {
        // 运维部门使用 save-ops-binding
        await DepartmentApi.saveOpsUserResourceBinding(bindingData)
      } else {
        // 运营部门使用 save-operation-binding
        await DepartmentApi.saveUserResourceBinding(bindingData)
      }
      ElMessage.success('资源绑定创建成功')
    }

    bindingDialogVisible.value = false
    editingBinding.value = null
    await refreshBindings()
  } catch (error) {
    if (error !== false) {
      // 不是表单验证错误
      console.error('保存资源绑定失败:', error)
      ElMessage.error('保存失败，请重试')
    }
  } finally {
    bindingSaving.value = false
  }
}

/** 批量添加资源绑定 */
const batchAddBindings = async () => {
  batchOperationType.value = 'add'
  resetBatchForm()

  // 根据部门类型设置默认资源类型
  if (isOperationDept()) {
    batchForm.resourceType = 'PRODUCT_CHANNEL'
  } else if (isOpsDept()) {
    batchForm.resourceType = 'OPS_RESOURCE'
  }

  batchDialogVisible.value = true
}

/** 重置批量表单 */
const resetBatchForm = () => {
  batchForm.operationMode = 'ADD' // 重置为默认的增量添加模式
  batchForm.resourceType = ''
  batchForm.productIds = []
  batchForm.channelCodes = []
  batchForm.productChannelCombinations = []
  batchForm.targetDeptIds = []
  batchForm.operationTeams = []
  batchForm.serverNames = []
  batchForm.status = 1
  batchForm.remark = ''
  batchPreviewList.value = []

  // 重置当前选择值
  currentBatchProduct.value = undefined
  currentBatchChannel.value = undefined
}

/** 批量资源类型变更处理 */
const handleBatchResourceTypeChange = async () => {
  // 清空相关字段
  batchForm.productIds = []
  batchForm.channelCodes = []
  batchForm.productChannelCombinations = []
  batchForm.targetDeptIds = []
  batchForm.operationTeams = []
  batchForm.serverNames = []
  batchPreviewList.value = []

  // 重置当前选择值
  currentBatchProduct.value = undefined
  currentBatchChannel.value = undefined

  // 根据资源类型加载对应的数据
  if (
    batchForm.resourceType === 'PRODUCT_CHANNEL' ||
    batchForm.resourceType === 'SERVER' ||
    batchForm.resourceType === 'OPS_RESOURCE'
  ) {
    channelList.value = []
  }
  if (batchForm.resourceType === 'OPERATION_TEAM' || batchForm.resourceType === 'OPS_RESOURCE') {
    operationTeamList.value = []
  }
}

/** 批量产品变更处理 */
const handleBatchProductChange = async () => {
  batchForm.channelCodes = []
  batchForm.serverNames = []

  // 根据选中的产品加载渠道列表
  if (batchForm.productIds.length > 0) {
    await loadChannelsForProducts(batchForm.productIds)
    if (batchForm.resourceType === 'SERVER') {
      await loadServersForProducts(batchForm.productIds)
    }
  } else {
    channelList.value = []
    serverList.value = []
  }

  updateBatchPreview()
}

/** 为指定产品列表加载渠道列表 */
const loadChannelsForProducts = async (productIds: number[]) => {
  try {
    console.log('为产品列表加载渠道:', productIds)
    const data = await request.get({
      url: '/erp/channel/list',
      params: {
        productId: productIds
      }
    })
    console.log('批量渠道API返回数据:', data)
    channelList.value = (data.list || data || []).map((item: any) => ({
      channelCode: item.channelCode,
      channelName: item.channelName || item.channelCode
    }))
    console.log('设置批量渠道列表:', channelList.value)
  } catch (error) {
    console.error('加载批量渠道列表失败:', error)
    // 降级使用模拟数据
    channelList.value = [
      { channelCode: 'c1', channelName: '官方渠道' },
      { channelCode: 'c2', channelName: '应用宝' },
      { channelCode: 'c3', channelName: '华为' },
      { channelCode: 'c4', channelName: '小米' }
    ]
    console.log('使用批量模拟渠道数据:', channelList.value)
  }
}

/** 为指定产品列表加载区服列表 */
const loadServersForProducts = async (productIds: number[]) => {
  try {
    console.log('为产品列表加载区服:', productIds)
    const data = await request.post({
      url: '/erp/order/GetServer',
      data: productIds
    })
    console.log('批量区服API返回数据:', data)
    serverList.value = ['全部', ...(data || [])]
    console.log('设置批量区服列表:', serverList.value)
  } catch (error) {
    console.error('加载批量区服列表失败:', error)
    // 降级使用模拟数据
    serverList.value = ['全部', '1服', '2服', '3服', '4服', '5服']
    console.log('使用批量模拟区服数据:', serverList.value)
  }
}

/** 批量目标部门变更处理 */
const handleBatchTargetDeptChange = async () => {
  batchForm.operationTeams = []
  // 这里需要根据选中的部门加载对应的运营队伍
  updateBatchPreview()
}

/** 添加产品渠道组合 */
const addProductChannelCombination = () => {
  if (currentBatchProduct.value && currentBatchChannel.value) {
    const newCombo = {
      productId: currentBatchProduct.value,
      channelCode: currentBatchChannel.value
    }

    // 检查是否已存在相同组合
    const exists = batchForm.productChannelCombinations.some(
      (combo) =>
        combo.productId === newCombo.productId && combo.channelCode === newCombo.channelCode
    )

    if (!exists) {
      batchForm.productChannelCombinations.push(newCombo)
      currentBatchProduct.value = undefined
      currentBatchChannel.value = undefined
      ElMessage.success('添加成功')
    } else {
      ElMessage.warning('该产品渠道组合已存在')
    }
  } else {
    ElMessage.warning('请选择产品和渠道')
  }
}

/** 删除产品渠道组合 */
const removeProductChannelCombination = (index: number) => {
  batchForm.productChannelCombinations.splice(index, 1)
  ElMessage.success('删除成功')
}

/** 清空所有产品渠道组合 */
const clearAllProductChannelCombinations = () => {
  batchForm.productChannelCombinations = []
  ElMessage.success('已清空所有组合')
}

/** 更新批量预览 */
const updateBatchPreview = () => {
  batchPreviewList.value = []

  if (batchForm.resourceType === 'PRODUCT_CHANNEL') {
    // 使用新的产品渠道组合数据结构
    batchForm.productChannelCombinations.forEach((combo) => {
      batchPreviewList.value.push({
        resourceType: 'PRODUCT_CHANNEL',
        productId: combo.productId,
        productName: getProductName(combo.productId),
        channelCode: combo.channelCode,
        channelName: getChannelName(combo.channelCode),
        status: batchForm.status,
        remark: batchForm.remark
      })
    })
  } else if (batchForm.resourceType === 'OPERATION_TEAM') {
    batchForm.targetDeptIds.forEach((targetDeptId) => {
      batchForm.operationTeams.forEach((operationTeam) => {
        batchPreviewList.value.push({
          resourceType: 'OPERATION_TEAM',
          targetDeptId,
          targetDeptName: getDeptName(targetDeptId),
          operationTeam,
          status: batchForm.status,
          remark: batchForm.remark
        })
      })
    })
  } else if (batchForm.resourceType === 'SERVER') {
    // 产品区服一一对应模式
    const minLength = Math.min(batchForm.productIds.length, batchForm.serverNames.length)
    for (let i = 0; i < minLength; i++) {
      const productId = batchForm.productIds[i]
      const serverName = batchForm.serverNames[i]
      batchPreviewList.value.push({
        resourceType: 'SERVER',
        productId,
        productName: getProductName(productId),
        serverName,
        status: batchForm.status,
        remark: batchForm.remark
      })
    }

    // 提示用户数量不匹配
    if (batchForm.productIds.length !== batchForm.serverNames.length) {
      ElMessage.warning(
        `产品数量(${batchForm.productIds.length})与区服数量(${batchForm.serverNames.length})不匹配，将按最小数量进行一一对应`
      )
    }
  } else if (batchForm.resourceType === 'OPS_RESOURCE') {
    // 运维资源类型，包含运营队伍和区服信息
    batchForm.opsResourceCombinations.forEach((combo) => {
      batchPreviewList.value.push({
        resourceType: 'OPS_RESOURCE',
        targetDeptId: combo.targetDeptId,
        targetDeptName: getDeptName(combo.targetDeptId),
        operationTeam: combo.operationTeam,
        productId: combo.productId,
        productName: getProductName(combo.productId),
        serverName: combo.serverName,
        status: batchForm.status,
        remark: batchForm.remark
      })
    })
  }
}

// 监听批量表单变化，更新预览
watch(
  () => [
    batchForm.productChannelCombinations,
    batchForm.targetDeptIds,
    batchForm.operationTeams,
    batchForm.serverNames,
    batchForm.status,
    batchForm.remark
  ],
  () => {
    updateBatchPreview()
  },
  { deep: true }
)

/** 批量保存 */
const handleBatchSave = async () => {
  if (batchOperationType.value === 'add') {
    await handleBatchAdd()
  } else {
    await handleBatchDelete()
  }
}

/** 批量添加处理 */
const handleBatchAdd = async () => {
  if (!batchFormRef.value) return

  try {
    await batchFormRef.value.validate()

    if (!selectedUserId.value || !selectedDept.value?.id) {
      ElMessage.warning('请先选择用户')
      return
    }

    if (batchPreviewList.value.length === 0) {
      ElMessage.warning('没有要创建的绑定')
      return
    }

    batchSaving.value = true

    // 构建批量操作请求
    const bindings: ResourceBindingItemVO[] = batchPreviewList.value.map((item) => ({
      resourceType: item.resourceType,
      productId: item.productId,
      channelCode: item.channelCode,
      operationTeam: item.operationTeam,
      serverName: item.serverName,
      targetDeptId: item.targetDeptId,
      opsDeptId: selectedDept.value?.id,
      selectedProductIds: item.productId ? [item.productId] : undefined,
      selectedChannelCodes: item.channelCode ? [item.channelCode] : undefined,
      operationTeams: item.operationTeam ? [item.operationTeam] : undefined,
      serverNames: item.serverName ? [item.serverName] : undefined,
      status: item.status,
      remark: item.remark
    }))

    const batchRequest: BatchResourceBindingReqVO = {
      userId: selectedUserId.value,
      operationMode: batchForm.operationMode, // 使用用户选择的操作模式
      bindings: bindings
    }

    console.log('批量操作请求数据:', batchRequest)

    // 根据部门类型使用不同的批量操作API
    if (isOpsDept()) {
      // 运维部门使用 ops 相关的批量操作API
      await DepartmentApi.batchOpsOperationBinding(batchRequest)
    } else {
      // 运营部门使用标准的批量操作API
      await DepartmentApi.batchOperationBinding(batchRequest)
    }

    const operationText =
      batchForm.operationMode === 'ADD'
        ? '添加'
        : batchForm.operationMode === 'REPLACE'
          ? '替换'
          : '操作'
    ElMessage.success(`成功${operationText} ${batchPreviewList.value.length} 条资源绑定`)

    batchDialogVisible.value = false
    await refreshBindings()
  } catch (error) {
    if (error !== false) {
      // 不是表单验证错误
      console.error('批量操作资源绑定失败:', error)
      ElMessage.error('批量操作失败，请重试')
    }
  } finally {
    batchSaving.value = false
  }
}

/** 批量删除处理 */
const handleBatchDelete = async () => {
  if (!selectedUserId.value || selectedBindings.value.length === 0) return

  try {
    batchSaving.value = true

    // 使用现有的批量删除逻辑
    const deletePromises = selectedBindings.value
      .filter((binding) => binding.id)
      .map((binding) => DepartmentApi.deleteUserResourceBinding(binding.id!))

    await Promise.all(deletePromises)
    ElMessage.success(`成功删除 ${selectedBindings.value.length} 条资源绑定`)

    batchDialogVisible.value = false
    await refreshBindings()
    selectedBindings.value = []
  } catch (error) {
    console.error('批量删除资源绑定失败:', error)
    ElMessage.error('批量删除失败，请重试')
  } finally {
    batchSaving.value = false
  }
}

/** 删除资源绑定 */
const deleteBinding = async (id: number) => {
  if (!selectedUserId.value) return

  try {
    await ElMessageBox.confirm(`确认删除该资源绑定吗？`, '系统提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await DepartmentApi.deleteUserResourceBinding(id)
    ElMessage.success('资源绑定删除成功')
    await refreshBindings()
  } catch (error) {
    console.error('删除资源绑定失败:', error)
  }
}

/** 复制资源绑定 */
const duplicateBinding = async (binding: UserResourceBindingVO) => {
  if (!selectedUserId.value) return

  try {
    await ElMessageBox.confirm(`确认复制该资源绑定吗？`, '系统提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 复制时移除ID并创建新绑定
    const newBinding = {
      userId: binding.userId,
      deptId: binding.deptId,
      resourceType: binding.resourceType,
      productIds: binding.productId ? [binding.productId] : undefined,
      channelCodes: binding.channelCode ? [binding.channelCode] : undefined,
      targetDeptId: binding.targetDeptId,
      teams: binding.operationTeam ? [binding.operationTeam] : undefined,
      serverNames: binding.serverName ? [binding.serverName] : undefined,
      status: binding.status,
      remark: binding.remark
    }

    await DepartmentApi.saveUserResourceBinding(newBinding)
    ElMessage.success('资源绑定复制成功')
    await refreshBindings()
  } catch (error) {
    console.error('复制资源绑定失败:', error)
  }
}

/** 刷新资源绑定列表 */
const refreshBindings = async () => {
  console.log('refreshBindings被调用，selectedUserId:', selectedUserId.value)
  if (!selectedUserId.value) {
    userResourceBindings.value = []
    return
  }
  bindingLoading.value = true
  try {
    console.log('开始调用API获取用户资源绑定')
    const data = await DepartmentApi.getUserResourceBindings(selectedUserId.value)
    console.log('API返回的数据:', data)
    userResourceBindings.value = data || []
    console.log('设置userResourceBindings:', userResourceBindings.value)
  } catch (error) {
    console.error('刷新资源绑定失败:', error)
    console.log('使用模拟数据作为降级处理')

    // 降级使用模拟数据
    userResourceBindings.value = [
      {
        id: 1,
        userId: selectedUserId.value,
        deptId: selectedDept.value?.id || 0,
        resourceType: 'PRODUCT_CHANNEL',
        productId: 1,
        channelCode: 'c1',
        status: 1,
        remark: '示例数据 - 产品渠道权限',
        createTime: Date.now()
      },
      {
        id: 2,
        userId: selectedUserId.value,
        deptId: selectedDept.value?.id || 0,
        resourceType: 'OPERATION_TEAM',
        operationTeam: '运营一组',
        targetDeptId: selectedDept.value?.id || 0,
        status: 1,
        remark: '示例数据 - 运营队伍权限',
        createTime: Date.now()
      }
    ]

    ElMessage.warning('使用模拟数据，请检查后端API')
  } finally {
    bindingLoading.value = false
  }
}

/** 批量删除资源绑定 */
const batchDeleteBindings = async () => {
  if (!selectedUserId.value || selectedBindings.value.length === 0) return

  try {
    await ElMessageBox.confirm(`确认批量删除选中的资源绑定吗？`, '系统提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    batchOperationType.value = 'delete'
    batchDialogVisible.value = true
  } catch (error) {
    console.error('批量删除资源绑定失败:', error)
  }
}

/** 处理资源绑定对话框成功 */
const handleBindingSuccess = () => {
  ElMessage.success('资源绑定保存成功')
  bindingDialogVisible.value = false
  editingBinding.value = null
  refreshBindings()
}

/** 获取权限类型统计 */
const getPermissionTypeStats = () => {
  const stats: { [key: string]: number } = {}
  if (permissionPreview.value?.bindingsByType) {
    Object.keys(permissionPreview.value.bindingsByType).forEach((type) => {
      stats[type] =
        permissionPreview.value.bindingsByType[
          type as keyof typeof permissionPreview.value.bindingsByType
        ]?.length || 0
    })
  }
  return stats
}

/** 获取权限类型图标 */
const getPermissionTypeIcon = (type: string) => {
  switch (type) {
    case 'PRODUCT_CHANNEL':
      return 'ep:box'
    case 'OPERATION_TEAM':
      return 'ep:user-filled'
    case 'SERVER':
      return 'ep:monitor'
    default:
      return 'ep:info'
  }
}

/** 获取资源类型文本 */
const getResourceTypeText = (type: string) => {
  switch (type) {
    case 'PRODUCT_CHANNEL':
      return '产品渠道'
    case 'OPERATION_TEAM':
      return '运营队伍'
    case 'SERVER':
      return '区服'
    case 'OPS_RESOURCE':
      return '运维资源'
    default:
      return type
  }
}

/** 获取资源类型标签类型 */
const getResourceTypeTagType = (type: string) => {
  switch (type) {
    case 'PRODUCT_CHANNEL':
      return 'primary'
    case 'OPERATION_TEAM':
      return 'success'
    case 'SERVER':
      return 'danger'
    case 'OPS_RESOURCE':
      return 'warning'
    default:
      return 'info'
  }
}

/** 格式化日期 */
const formatDate = (timestamp: string | number) => {
  const date = new Date(timestamp)
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

/** 导出权限报告 */
const exportPermissionReport = () => {
  if (!previewUserId.value) {
    ElMessage.warning('请先选择用户')
    return
  }

  const data = permissionPreview.value
  const userName = getPreviewUserName()
  const content =
    `用户权限报告\n` +
    `================\n\n` +
    `用户: ${userName}\n` +
    `部门: ${selectedDept.value?.deptName || ''}\n` +
    `生成时间: ${new Date().toLocaleString()}\n\n` +
    `权限统计:\n` +
    `- 权限绑定总数: ${data.totalBindings || 0}\n` +
    `- 产品权限数: ${data.productCount || 0}\n` +
    `- 渠道权限数: ${data.channelCount || 0}\n` +
    `- 运营队伍权限数: ${data.teamCount || 0}\n` +
    `- 区服权限数: ${data.serverCount || 0}\n\n` +
    `权限配置状态: ${data.hasBindings ? '已配置' : '未配置'}\n\n` +
    `权限类型分布:\n` +
    `- 产品渠道权限: ${data.bindingsByType?.PRODUCT_CHANNEL?.length || 0} 个\n` +
    `- 运营队伍权限: ${data.bindingsByType?.OPERATION_TEAM?.length || 0} 个\n` +
    `- 区服权限: ${data.bindingsByType?.SERVER?.length || 0} 个\n\n` +
    `权限汇总:\n` +
    `- 可访问产品: ${data.boundProductIds?.map((id: number) => getProductName(id)).join(', ') || '暂无'}\n` +
    `- 可访问渠道: ${data.boundChannelCodes?.join(', ') || '暂无'}\n` +
    `- 可管理运营队伍: ${data.boundOperationTeams?.join(', ') || '暂无'}\n` +
    `- 可管理区服: ${data.boundServerNames?.join(', ') || '暂无'}\n\n` +
    `详细权限信息:\n` +
    `================\n\n` +
    `产品渠道权限:\n` +
    (data.bindingsByType?.PRODUCT_CHANNEL?.map(
      (b: any) =>
        `- 产品: ${getProductName(b.productId)}, 渠道: ${b.channelCode}, 状态: ${b.status === 1 ? '启用' : '停用'}, 备注: ${b.remark || '无'}`
    ).join('\n') || '暂无产品渠道权限') +
    '\n\n' +
    `运营队伍权限:\n` +
    (data.bindingsByType?.OPERATION_TEAM?.map(
      (b: any) =>
        `- 运营队伍: ${b.operationTeam}, 目标部门: ${getDeptName(b.targetDeptId)}, 状态: ${b.status === 1 ? '启用' : '停用'}, 备注: ${b.remark || '无'}`
    ).join('\n') || '暂无运营队伍权限') +
    '\n\n' +
    `区服权限:\n` +
    (data.bindingsByType?.SERVER?.map(
      (b: any) =>
        `- 产品: ${getProductName(b.productId)}, 区服: ${b.serverName}, 渠道: ${b.channelCode}, 状态: ${b.status === 1 ? '启用' : '停用'}, 备注: ${b.remark || '无'}`
    ).join('\n') || '暂无区服权限')

  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${userName}_权限报告_${new Date().toLocaleDateString()}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

/** 保存所有更改 */
const saveAllChanges = async () => {
  saving.value = true
  try {
    // 保存当前编辑的绑定
    if (editingBinding.value) {
      if (editingBinding.value.id) {
        // 更新现有绑定 - 根据部门类型使用不同的API
        if (isOpsDept()) {
          await DepartmentApi.saveOpsUserResourceBinding({
            userId: editingBinding.value.userId,
            deptId: editingBinding.value.deptId,
            resourceType: editingBinding.value.resourceType,
            productIds: editingBinding.value.productId
              ? [editingBinding.value.productId]
              : undefined,
            channelCodes: editingBinding.value.channelCode
              ? [editingBinding.value.channelCode]
              : undefined,
            targetDeptId: editingBinding.value.targetDeptId,
            teams: editingBinding.value.operationTeam
              ? [editingBinding.value.operationTeam]
              : undefined,
            serverNames: editingBinding.value.serverName
              ? [editingBinding.value.serverName]
              : undefined,
            status: editingBinding.value.status,
            remark: editingBinding.value.remark
          })
        } else {
          await DepartmentApi.saveUserResourceBinding({
            userId: editingBinding.value.userId,
            deptId: editingBinding.value.deptId,
            resourceType: editingBinding.value.resourceType,
            productIds: editingBinding.value.productId
              ? [editingBinding.value.productId]
              : undefined,
            channelCodes: editingBinding.value.channelCode
              ? [editingBinding.value.channelCode]
              : undefined,
            targetDeptId: editingBinding.value.targetDeptId,
            teams: editingBinding.value.operationTeam
              ? [editingBinding.value.operationTeam]
              : undefined,
            serverNames: editingBinding.value.serverName
              ? [editingBinding.value.serverName]
              : undefined,
            status: editingBinding.value.status,
            remark: editingBinding.value.remark
          })
        }
        ElMessage.success('资源绑定更新成功')
      } else {
        // 创建新绑定 - 根据部门类型使用不同的API
        if (isOpsDept()) {
          await DepartmentApi.saveOpsUserResourceBinding({
            userId: editingBinding.value.userId,
            deptId: editingBinding.value.deptId,
            resourceType: editingBinding.value.resourceType,
            productIds: editingBinding.value.productId
              ? [editingBinding.value.productId]
              : undefined,
            channelCodes: editingBinding.value.channelCode
              ? [editingBinding.value.channelCode]
              : undefined,
            targetDeptId: editingBinding.value.targetDeptId,
            teams: editingBinding.value.operationTeam
              ? [editingBinding.value.operationTeam]
              : undefined,
            serverNames: editingBinding.value.serverName
              ? [editingBinding.value.serverName]
              : undefined,
            status: editingBinding.value.status,
            remark: editingBinding.value.remark
          })
        } else {
          await DepartmentApi.saveUserResourceBinding({
            userId: editingBinding.value.userId,
            deptId: editingBinding.value.deptId,
            resourceType: editingBinding.value.resourceType,
            productIds: editingBinding.value.productId
              ? [editingBinding.value.productId]
              : undefined,
            channelCodes: editingBinding.value.channelCode
              ? [editingBinding.value.channelCode]
              : undefined,
            targetDeptId: editingBinding.value.targetDeptId,
            teams: editingBinding.value.operationTeam
              ? [editingBinding.value.operationTeam]
              : undefined,
            serverNames: editingBinding.value.serverName
              ? [editingBinding.value.serverName]
              : undefined,
            status: editingBinding.value.status,
            remark: editingBinding.value.remark
          })
        }
        ElMessage.success('资源绑定创建成功')
      }
    }

    await refreshBindings()
    selectedBindings.value = []
    editingBinding.value = null
  } catch (error) {
    console.error('保存所有更改失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

/** 处理批量操作成功 */
const handleBatchSuccess = () => {
  ElMessage.success('批量操作成功')
  batchDialogVisible.value = false
  selectedBindings.value = []
  refreshBindings()
}

/** 用户分页变化 */
const handleUserPageChange = (page: number) => {
  userPageParams.pageNo = page
  loadAvailableUsers()
}

/** 用户页面大小变化 */
const handleUserPageSizeChange = (pageSize: number) => {
  userPageParams.pageSize = pageSize
  userPageParams.pageNo = 1
  loadAvailableUsers()
}

/** 当前批量产品变更处理 */
const handleCurrentBatchProductChange = async () => {
  // 清空渠道选择
  currentBatchChannel.value = undefined

  // 根据选中的产品加载渠道列表
  if (currentBatchProduct.value) {
    channelLoading.value = true
    try {
      await loadChannelsForProduct(currentBatchProduct.value)
    } finally {
      channelLoading.value = false
    }
  } else {
    channelList.value = []
  }
}

/** 根据部门自动分配角色 */
const getAutoRoleByDepartment = () => {
  if (!selectedDept.value) return 'TEAM_MEMBER' // 默认组员

  const deptName = selectedDept.value.deptName || ''
  const deptLevel = selectedDept.value.deptLevel

  // 优先使用部门层级字段判断角色
  if (deptLevel) {
    switch (deptLevel) {
      case 1:
        return 'SUPERVISOR' // 主管部门 -> 主管角色
      case 2:
        return 'TEAM_LEADER' // 小组 -> 组长角色（默认）
      default:
        return 'TEAM_MEMBER' // 默认组员
    }
  }

  // 降级处理：如果没有层级字段，则根据部门名称判断
  if (deptName.includes('主管') || deptName.includes('管理') || deptName.includes('总监')) {
    return 'SUPERVISOR' // 主管
  } else if (deptName.includes('组长') || deptName.includes('小组') || deptName.includes('队长')) {
    return 'TEAM_LEADER' // 组长
  } else {
    return 'TEAM_MEMBER' // 组员（默认）
  }
}

/** 根据当前部门自动判断关系类型 */
const getCurrentRelationType = () => {
  if (!selectedDept.value) return 3 // 默认组员

  // 根据部门层级判断关系类型
  if (selectedDept.value.deptLevel === 1) {
    return 1 // 主管部门 - 主管
  } else if (selectedDept.value.deptLevel === 2) {
    return 2 // 小组 - 组长
  } else {
    return 3 // 默认组员
  }
}

/** 获取当前关系类型文本 */
const getCurrentRelationTypeText = () => {
  const relationType = getCurrentRelationType()
  const autoRole = getAutoRoleByDepartment()

  // 对于小组（第2层），提示用户可以手动选择角色
  if (selectedDept.value?.deptLevel === 2) {
    return `分配为${getRelationTypeText(relationType)}，可选择角色：组长或组员`
  }

  // 对于主管部门（第1层），自动分配主管角色
  return `分配为${getRelationTypeText(relationType)}，角色：${getRoleText(autoRole)}`
}

/** 获取分配按钮类型 */
const getAssignButtonType = () => {
  const relationType = getCurrentRelationType()
  switch (relationType) {
    case 1:
      return 'danger' // 主管 - 红色
    case 2:
      return 'warning' // 组长 - 橙色
    case 3:
      return 'success' // 组员 - 绿色
    default:
      return 'primary' // 默认 - 蓝色
  }
}

/** 查看用户权限 */
const viewUserPermissions = async (userId: number) => {
  try {
    const userBindings = await DepartmentApi.getUserResourceBindings(userId)
    const user = assignedUsers.value.find((u) => u.userId === userId)
    const userName = user?.userName || user?.username || `用户${userId}`

    if (!userBindings || userBindings.length === 0) {
      ElMessageBox.alert(`用户 ${userName} 暂无权限绑定`, '权限信息', {
        confirmButtonText: '确定',
        type: 'info'
      })
      return
    }

    // 分类统计权限绑定
    const productChannelBindings = userBindings.filter((b) => b.resourceType === 'PRODUCT_CHANNEL')
    const operationTeamBindings = userBindings.filter((b) => b.resourceType === 'OPERATION_TEAM')
    const serverBindings = userBindings.filter((b) => b.resourceType === 'SERVER')

    let permissionDetails = `用户 ${userName} 的权限绑定详情：\n\n`
    permissionDetails += `📊 权限统计：\n`
    permissionDetails += `• 总计：${userBindings.length} 条权限绑定\n`

    if (productChannelBindings.length > 0) {
      permissionDetails += `\n\n【产品渠道权限】\n`
      productChannelBindings.forEach((b) => {
        permissionDetails += `- 产品: ${getProductName(b.productId)}, 渠道: ${b.channelCode}, 状态: ${b.status === 1 ? '启用' : '停用'}, 备注: ${b.remark || '无'}\n`
      })
    }
    if (operationTeamBindings.length > 0) {
      permissionDetails += `\n【运营队伍权限】\n`
      operationTeamBindings.forEach((b) => {
        permissionDetails += `- 运营队伍: ${b.operationTeam}, 目标部门: ${getDeptName(b.targetDeptId)}, 状态: ${b.status === 1 ? '启用' : '停用'}, 备注: ${b.remark || '无'}\n`
      })
    }
    if (serverBindings.length > 0) {
      permissionDetails += `\n【区服权限】\n`
      serverBindings.forEach((b) => {
        permissionDetails += `- 产品: ${getProductName(b.productId)}, 区服: ${b.serverName}, 渠道: ${b.channelCode}, 状态: ${b.status === 1 ? '启用' : '停用'}, 备注: ${b.remark || '无'}\n`
      })
    }
    if (
      productChannelBindings.length === 0 &&
      operationTeamBindings.length === 0 &&
      serverBindings.length === 0
    ) {
      permissionDetails += '\n无详细权限数据'
    }
    await ElMessageBox.alert(
      `<pre style=\"text-align:left;white-space:pre-wrap;\">${permissionDetails}</pre>`,
      `用户 ${userName} 权限详情`,
      {
        confirmButtonText: '确定',
        type: 'info',
        dangerouslyUseHTMLString: true,
        customClass: 'user-permission-detail-dialog'
      }
    )
  } catch (error) {
    console.error('查看用户权限失败:', error)
    ElMessage.error('加载权限详情失败')
  }
}

// 暴露方法给父组件
defineExpose({
  open
})
</script>

<style scoped>
.role-assign-form {
  padding: 10px 0;
}

.simple-role-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.simple-role-radio {
  width: 100%;
  margin: 0 !important;
  padding: 12px 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  background-color: #fff;
}

.simple-role-radio:hover {
  border-color: #409eff;
  background-color: #f5f7fa;
}

.simple-role-radio.is-checked {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.simple-role-radio :deep(.el-radio__input) {
  margin-right: 8px;
}

.simple-role-radio :deep(.el-radio__label) {
  width: 100%;
  padding-left: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.role-label {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.role-tag {
  margin-left: 8px;
}

.selected-users-list {
  max-height: 120px;
  overflow-y: auto;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafbfc;
  min-height: 60px;
}

.selected-users-list:empty::before {
  content: '暂无选中用户';
  color: #c0c4cc;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 36px;
}

.dept-auto-role-tag {
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 角色分配对话框样式 */
:deep(.role-assign-dialog .el-dialog__body) {
  padding: 20px 30px;
}

:deep(.role-assign-dialog .el-form-item__content) {
  width: 100%;
}

:deep(.simple-role-group .el-radio) {
  width: 100%;
  height: auto;
  margin-right: 0;
  margin-bottom: 0;
}

:deep(.simple-role-group .el-radio__label) {
  width: 100%;
  padding-left: 0;
}
</style>
