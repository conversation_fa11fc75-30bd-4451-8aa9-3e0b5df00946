<template>
  <div class="performance-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="title-section">
            <Icon icon="ep:data-analysis" class="title-icon" />
            <div class="title-text">
              <h1 class="page-title">业绩管理中心</h1>
              <p class="page-subtitle">基于权限的实时业绩数据分析</p>
            </div>
          </div>
        </div>
        <div class="header-right">
          <div class="user-info">
            <el-tag :type="getRoleTagType(userRole)" size="large" class="role-badge">
              <Icon :icon="getRoleIcon(userRole)" class="role-icon" />
              {{ getRoleText(userRole) }}
            </el-tag>
            <div class="update-time">
              <span class="time-label">数据更新</span>
              <span class="time-value">{{ formatTime(lastUpdateTime) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <el-card class="filter-card" shadow="never">
        <div class="filter-header">
          <div class="filter-title">
            <Icon icon="ep:filter" />
            <span>筛选条件</span>
          </div>
          <el-button
            type="primary"
            @click="loadPerformanceData"
            :loading="loading"
            class="refresh-btn"
          >
            <Icon icon="ep:refresh" />
            刷新数据
          </el-button>
        </div>

        <div class="filter-content">
          <el-form :model="filterParams" :inline="true" class="filter-form">
            <el-form-item label="查询时间">
              <el-date-picker
                v-model="filterParams.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                @change="handleDateRangeChange"
                :shortcuts="dateShortcuts"
                class="date-picker"
              />
            </el-form-item>

            <el-form-item label="产品筛选">
              <el-select
                v-model="filterParams.productIds"
                placeholder="全部产品"
                multiple
                collapse-tags
                collapse-tags-tooltip
                clearable
                @change="handleProductChange"
                class="product-select"
              >
                <el-option
                  v-for="product in productList"
                  :key="product.id"
                  :label="product.name"
                  :value="product.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="handleSearch" :loading="loading">
                <Icon icon="ep:search" />
                查询
              </el-button>
              <el-button @click="resetFilter">
                <Icon icon="ep:refresh" />
                重置
              </el-button>
              <el-button @click="exportData" :loading="exportLoading">
                <Icon icon="ep:download" />
                导出
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>

    <!-- 主要内容区域 -->
    <div v-loading="loading" class="main-content">
      <!-- 无权限提示 -->
      <template v-if="userRole === 'NONE'">
        <div class="empty-state">
          <el-card class="empty-card" shadow="never">
            <div class="empty-content">
              <Icon icon="ep:lock" class="empty-icon" />
              <h3 class="empty-title">暂无查看权限</h3>
              <p class="empty-desc">您暂无查看业绩的权限，请联系管理员开通相关权限</p>
              <el-button type="primary" @click="$router.back()">
                <Icon icon="ep:arrow-left" />
                返回上页
              </el-button>
            </div>
          </el-card>
        </div>
      </template>

      <!-- 有权限的内容 -->
      <template v-else>
        <!-- 业绩概览卡片 -->
        <div class="overview-section">
          <div class="overview-grid">
            <div class="stat-card primary">
              <div class="stat-icon">
                <Icon icon="ep:money" />
              </div>
              <div class="stat-content">
                <div class="stat-value">{{
                  formatCurrency(performanceData.departmentTotal || 0)
                }}</div>
                <div class="stat-label">总业绩</div>
              </div>
              <div class="stat-trend">
                <Icon icon="ep:top" class="trend-icon up" />
                <span class="trend-text">较上期增长</span>
              </div>
            </div>

            <div class="stat-card success">
              <div class="stat-icon">
                <Icon icon="ep:user-filled" />
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ getTotalMemberCount() }}</div>
                <div class="stat-label">总人数</div>
              </div>
              <div class="stat-trend">
                <Icon icon="ep:user" class="trend-icon" />
                <span class="trend-text">活跃成员</span>
              </div>
            </div>

            <div class="stat-card warning">
              <div class="stat-icon">
                <Icon icon="ep:office-building" />
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ getDepartmentCount() }}</div>
                <div class="stat-label">部门数</div>
              </div>
              <div class="stat-trend">
                <Icon icon="ep:collection" class="trend-icon" />
                <span class="trend-text">组织架构</span>
              </div>
            </div>

            <div class="stat-card info">
              <div class="stat-icon">
                <Icon icon="ep:trophy" />
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatCurrency(getAveragePerformance()) }}</div>
                <div class="stat-label">人均业绩</div>
              </div>
              <div class="stat-trend">
                <Icon icon="ep:trend-charts" class="trend-icon" />
                <span class="trend-text">平均水平</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 权限说明卡片 -->
        <div class="permission-section">
          <el-card class="permission-card" shadow="never">
            <div class="permission-content">
              <div class="permission-left">
                <div class="permission-icon">
                  <Icon :icon="getRoleIcon(userRole)" />
                </div>
                <div class="permission-info">
                  <h3 class="permission-title">{{ getRoleText(userRole) }}权限说明</h3>
                  <p class="permission-desc">{{ getRoleDescription(userRole) }}</p>
                </div>
              </div>
              <div class="permission-right">
                <div class="data-status">
                  <div class="status-item">
                    <span class="status-label">查询时间段</span>
                    <span class="status-value">{{ formatDateRange(filterParams.dateRange) }}</span>
                  </div>
                  <div class="status-item">
                    <span class="status-label">数据状态</span>
                    <el-tag type="success" size="small">
                      <Icon icon="ep:lightning" />
                      实时更新
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 层级化业绩数据 -->
        <div class="hierarchy-section">
          <el-card class="hierarchy-card" shadow="never">
            <template #header>
              <div class="hierarchy-header">
                <div class="hierarchy-title">
                  <Icon icon="ep:operation" />
                  <span>层级化业绩视图</span>
                </div>
                <div class="hierarchy-actions">
                  <el-tag type="info" size="small">
                    {{ performanceData.hierarchyData?.length || 0 }}个顶级节点
                  </el-tag>
                </div>
              </div>
            </template>

            <div class="hierarchy-content">
              <HierarchyTreeView
                v-if="performanceData.hierarchyData && performanceData.hierarchyData.length > 0"
                :hierarchy-data="performanceData.hierarchyData"
                :user-role="userRole"
                :filter-params="filterParams"
                :loading="loading"
                @expand="handleNodeExpand"
                @collapse="handleNodeCollapse"
                @view-detail="handleViewDetail"
                @refresh="loadPerformanceData"
              />
              <div v-else class="hierarchy-empty">
                <Icon icon="ep:document" class="empty-icon" />
                <p class="empty-text">暂无层级数据</p>
              </div>
            </div>
          </el-card>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import {
  PerformanceApi,
  type PerformanceDataVO,
  type PerformancePageReqVO
} from '@/api/erp/performance'
import HierarchyTreeView from './components/HierarchyTreeView.vue'

defineOptions({ name: 'PerformanceIndex' })

// 用户角色类型
type UserRole = 'SUPER_ADMIN' | 'SUPERVISOR' | 'TEAM_LEADER' | 'TEAM_MEMBER' | 'NONE'

const loading = ref(false)
const exportLoading = ref(false)

// 表单引用
const filterFormRef = ref()

// 用户store
const userStore = useUserStore()

// 筛选参数
const filterParams = reactive({
  dateRange: [] as string[], // 日期范围 ['2024-01-01', '2024-01-31']
  productIds: [] as number[] // 产品ID列表
})

// 产品列表
const productList = ref([
  { id: 1, name: '传奇世界' },
  { id: 2, name: '魔域手游' },
  { id: 3, name: '仙境传说' },
  { id: 4, name: '神武4' },
  { id: 5, name: '大话西游' }
])

// 用户角色
const userRole = ref<UserRole>('NONE')

// 业绩数据
const performanceData = ref<PerformanceDataVO>({})

// 最后更新时间
const lastUpdateTime = ref<Date>(new Date())

// 日期快捷选项
const dateShortcuts = [
  {
    text: '今天',
    value: () => {
      const today = new Date()
      const todayStr = today.toISOString().slice(0, 10)
      return [todayStr, todayStr]
    }
  },
  {
    text: '昨天',
    value: () => {
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      const yesterdayStr = yesterday.toISOString().slice(0, 10)
      return [yesterdayStr, yesterdayStr]
    }
  },
  {
    text: '本周',
    value: () => {
      const now = new Date()
      const monday = new Date(now)
      monday.setDate(now.getDate() - now.getDay() + 1)
      const mondayStr = monday.toISOString().slice(0, 10)
      const todayStr = now.toISOString().slice(0, 10)
      return [mondayStr, todayStr]
    }
  },
  {
    text: '本月',
    value: () => {
      const now = new Date()
      const firstDay = new Date(now.getFullYear(), now.getMonth(), 1)
      const firstDayStr = firstDay.toISOString().slice(0, 10)
      const todayStr = now.toISOString().slice(0, 10)
      return [firstDayStr, todayStr]
    }
  },
  {
    text: '最近7天',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(end.getDate() - 6)
      return [start.toISOString().slice(0, 10), end.toISOString().slice(0, 10)]
    }
  },
  {
    text: '最近30天',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(end.getDate() - 29)
      return [start.toISOString().slice(0, 10), end.toISOString().slice(0, 10)]
    }
  }
]

/** 获取角色标签类型 */
const getRoleTagType = (role: UserRole) => {
  switch (role) {
    case 'SUPER_ADMIN':
      return 'danger'
    case 'SUPERVISOR':
      return 'danger'
    case 'TEAM_LEADER':
      return 'warning'
    case 'TEAM_MEMBER':
      return 'success'
    default:
      return 'info'
  }
}

/** 获取角色图标 */
const getRoleIcon = (role: UserRole) => {
  switch (role) {
    case 'SUPER_ADMIN':
      return 'ep:crown'
    case 'SUPERVISOR':
      return 'ep:user-filled'
    case 'TEAM_LEADER':
      return 'ep:avatar'
    case 'TEAM_MEMBER':
      return 'ep:user'
    default:
      return 'ep:question-filled'
  }
}

/** 获取角色文本 */
const getRoleText = (role: UserRole) => {
  switch (role) {
    case 'SUPER_ADMIN':
      return '超级管理员'
    case 'SUPERVISOR':
      return '部门主管'
    case 'TEAM_LEADER':
      return '组长'
    case 'TEAM_MEMBER':
      return '组员'
    default:
      return '未知角色'
  }
}

/** 获取角色描述 */
const getRoleDescription = (role: UserRole) => {
  switch (role) {
    case 'SUPER_ADMIN':
      return '您拥有最高权限，可以查看全公司所有产品、渠道、区服的业绩数据，不受资源绑定限制'
    case 'SUPERVISOR':
      return '您可以查看所有下属用户绑定的资源范围内的业绩数据，包括产品+渠道+区服的组合权限'
    case 'TEAM_LEADER':
      return '您可以查看本组成员绑定的资源范围内的业绩数据'
    case 'TEAM_MEMBER':
      return '您只能查看自己绑定的产品、渠道、区服权限范围内的业绩数据'
    default:
      return '系统未能识别您的权限级别，请联系管理员配置资源绑定权限'
  }
}

/** 初始化筛选参数 */
const initFilterParams = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')

  // 设置默认为当月第一天到今天
  const firstDay = `${year}-${month}-01`
  const today = now.toISOString().slice(0, 10)

  filterParams.dateRange = [firstDay, today]
  filterParams.productIds = []
}

/** 加载用户角色 */
const loadUserRole = async () => {
  try {
    const role = await PerformanceApi.getUserRole()
    userRole.value = role as UserRole
  } catch (error) {
    console.error('获取用户角色失败:', error)
    ElMessage.error('获取用户角色失败')
  }
}

/** 加载产品列表 */
const loadProductList = async () => {
  try {
    // TODO: 调用API获取产品列表
    // const products = await ProductApi.getProductList()
    // productList.value = products

    // 模拟数据
    productList.value = [
      { id: 1, name: '传奇世界' },
      { id: 2, name: '魔域手游' },
      { id: 3, name: '仙境传说' },
      { id: 4, name: '神武4' },
      { id: 5, name: '大话西游' }
    ]
  } catch (error) {
    console.error('加载产品列表失败:', error)
  }
}

/** 加载业绩数据 */
const loadPerformanceData = async () => {
  try {
    loading.value = true

    // 构建查询参数
    const queryParams: PerformancePageReqVO = {
      startDate: filterParams.dateRange[0],
      endDate: filterParams.dateRange[1],
      productIds: filterParams.productIds.length > 0 ? filterParams.productIds : undefined
    }

    console.log('查询参数:', queryParams)

    // 调用API获取业绩数据
    performanceData.value = await PerformanceApi.getPerformanceData(queryParams)

    // 调试日志：检查返回的数据结构
    console.log('返回的业绩数据:', performanceData.value)
    console.log('层级数据字段:', performanceData.value.hierarchyData)
    console.log('部门总计字段:', performanceData.value.departmentTotal)

    // 更新最后更新时间
    lastUpdateTime.value = new Date()
  } catch (error) {
    console.error('加载业绩数据失败:', error)
    ElMessage.error('加载业绩数据失败')
  } finally {
    loading.value = false
  }
}

/** 处理日期范围变化 */
const handleDateRangeChange = (dateRange: string[]) => {
  console.log('日期范围变化:', dateRange)
  if (dateRange && dateRange.length === 2) {
    loadPerformanceData()
  }
}

/** 处理产品变化 */
const handleProductChange = (productIds: number[]) => {
  console.log('产品筛选变化:', productIds)
  loadPerformanceData()
}

/** 手动搜索 */
const handleSearch = () => {
  loadPerformanceData()
}

/** 重置筛选条件 */
const resetFilter = () => {
  initFilterParams()
  loadPerformanceData()
}

/** 处理节点展开 */
const handleNodeExpand = (nodeId: string) => {
  console.log('节点展开:', nodeId)
  // TODO: 可以在这里加载更详细的子节点数据
}

/** 处理节点收起 */
const handleNodeCollapse = (nodeId: string) => {
  console.log('节点收起:', nodeId)
}

/** 处理查看详情 */
const handleViewDetail = (nodeId: string, nodeType: string) => {
  console.log('查看详情:', nodeId, nodeType)
  // TODO: 打开详情弹窗或跳转到详情页面
}

/** 格式化金额 */
const formatCurrency = (amount: number) => {
  return amount.toLocaleString()
}

/** 格式化时间 */
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

/** 格式化日期范围 */
const formatDateRange = (dateRange: string[]) => {
  if (!dateRange || dateRange.length === 0) return '未选择日期'
  if (dateRange.length === 1 || dateRange[0] === dateRange[1]) {
    return dateRange[0]
  }
  return `${dateRange[0]} 至 ${dateRange[1]}`
}

/** 获取总人数 */
const getTotalMemberCount = () => {
  // 首先检查新的层级数据结构
  if (performanceData.value.hierarchyData && performanceData.value.hierarchyData.length > 0) {
    const countMembers = (nodes: any[]): number => {
      let count = 0
      for (const node of nodes) {
        if (node.nodeType === 'PERSON') {
          count++
        } else if (node.children) {
          count += countMembers(node.children)
        }
      }
      return count
    }
    return countMembers(performanceData.value.hierarchyData)
  }

  // 向后兼容旧的数据结构
  if (performanceData.value.productBreakdown) {
    let totalCount = 0
    performanceData.value.productBreakdown.forEach((product) => {
      if (product.teamData) {
        product.teamData.forEach((team) => {
          totalCount += team.memberCount || 0
        })
      }
      if (product.personalData) {
        totalCount += 1
      }
    })
    return totalCount
  }

  return 0
}

/** 获取部门数量 */
const getDepartmentCount = () => {
  // 首先检查新的层级数据结构
  if (performanceData.value.hierarchyData && performanceData.value.hierarchyData.length > 0) {
    const countDepartments = (nodes: any[]): number => {
      let count = 0
      for (const node of nodes) {
        if (node.nodeType === 'DEPARTMENT') {
          count++
        }
        if (node.children) {
          count += countDepartments(node.children)
        }
      }
      return count
    }
    return countDepartments(performanceData.value.hierarchyData)
  }

  // 向后兼容：如果没有层级数据，返回产品数量作为近似值
  if (performanceData.value.productBreakdown) {
    return performanceData.value.productBreakdown.length
  }

  return 0
}

/** 获取平均业绩 */
const getAveragePerformance = () => {
  const totalMembers = getTotalMemberCount()
  if (totalMembers === 0) return 0
  return Math.round((performanceData.value.departmentTotal || 0) / totalMembers)
}

/** 导出数据 */
const exportData = async () => {
  try {
    exportLoading.value = true
    // TODO: 实现导出功能
    ElMessage.success('导出功能开发中...')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

/** 初始化页面 */
onMounted(async () => {
  initFilterParams()
  await loadUserRole()
  await loadProductList()
  await loadPerformanceData()
})
</script>

<style scoped>
.performance-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 0;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
  padding: 0;
  margin-bottom: 24px;
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(99, 102, 241, 0.1) 0%,
    rgba(168, 85, 247, 0.08) 50%,
    rgba(236, 72, 153, 0.06) 100%
  );
  z-index: 1;
}

.header-content {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px 40px;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left {
  flex: 1;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.title-icon {
  font-size: 48px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3));
}

.title-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.page-title {
  margin: 0;
  font-size: 32px;
  font-weight: 700;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  margin: 0;
  font-size: 16px;
  color: #cbd5e1;
  font-weight: 400;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.role-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 600;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.role-icon {
  font-size: 16px;
}

.update-time {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
  font-size: 12px;
}

.time-label {
  color: #94a3b8;
}

.time-value {
  color: #e2e8f0;
  font-weight: 500;
}

/* 筛选区域 */
.filter-section {
  max-width: 1400px;
  margin: 0 auto 24px;
  padding: 0 24px;
}

.filter-card {
  border-radius: 16px;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f1f5f9;
}

.filter-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #334155;
}

.filter-title .el-icon {
  color: #6366f1;
}

.refresh-btn {
  border-radius: 8px;
  padding: 8px 16px;
}

.filter-content {
  padding: 20px 24px;
}

.filter-form {
  margin: 0;
}

.filter-form :deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 24px;
}

.filter-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #475569;
}

.date-picker {
  width: 280px;
}

.product-select {
  width: 240px;
}

/* 主要内容区域 */
.main-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.empty-card {
  border-radius: 16px;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 40px;
}

.empty-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.empty-icon {
  font-size: 64px;
  color: #94a3b8;
}

.empty-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #334155;
}

.empty-desc {
  margin: 0;
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
}

/* 业绩概览区域 */
.overview-section {
  margin-bottom: 24px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  transition: all 0.3s ease;
}

.stat-card.primary::before {
  background: linear-gradient(180deg, #6366f1 0%, #4f46e5 100%);
}

.stat-card.success::before {
  background: linear-gradient(180deg, #10b981 0%, #059669 100%);
}

.stat-card.warning::before {
  background: linear-gradient(180deg, #f59e0b 0%, #d97706 100%);
}

.stat-card.info::before {
  background: linear-gradient(180deg, #06b6d4 0%, #0891b2 100%);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-card {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  flex-shrink: 0;
}

.stat-card.primary .stat-icon {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(79, 70, 229, 0.15) 100%);
  color: #4f46e5;
}

.stat-card.success .stat-icon {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.15) 100%);
  color: #059669;
}

.stat-card.warning .stat-icon {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.15) 100%);
  color: #d97706;
}

.stat-card.info .stat-icon {
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.1) 0%, rgba(8, 145, 178, 0.15) 100%);
  color: #0891b2;
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 8px;
}

.trend-icon {
  font-size: 12px;
  color: #64748b;
}

.trend-icon.up {
  color: #10b981;
}

.trend-text {
  font-size: 12px;
  color: #64748b;
}

/* 权限说明区域 */
.permission-section {
  margin-bottom: 24px;
}

.permission-card {
  border-radius: 16px;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.permission-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
}

.permission-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.permission-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(79, 70, 229, 0.15) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #4f46e5;
  flex-shrink: 0;
}

.permission-info {
  flex: 1;
}

.permission-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.permission-desc {
  margin: 0;
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
}

.permission-right {
  display: flex;
  align-items: center;
}

.data-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.status-label {
  font-size: 12px;
  color: #94a3b8;
}

.status-value {
  font-size: 14px;
  font-weight: 500;
  color: #334155;
}

/* 层级化数据区域 */
.hierarchy-section {
  margin-bottom: 24px;
}

.hierarchy-card {
  border-radius: 16px;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.hierarchy-card :deep(.el-card__header) {
  padding: 20px 24px;
  border-bottom: 1px solid #f1f5f9;
}

.hierarchy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.hierarchy-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #334155;
}

.hierarchy-title .el-icon {
  color: #6366f1;
}

.hierarchy-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.hierarchy-content {
  padding: 24px;
  min-height: 200px;
}

.hierarchy-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  min-height: 200px;
}

.hierarchy-empty .empty-icon {
  font-size: 48px;
  color: #cbd5e1;
}

.hierarchy-empty .empty-text {
  margin: 0;
  font-size: 14px;
  color: #94a3b8;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    padding: 24px 32px;
  }

  .page-title {
    font-size: 28px;
  }

  .title-icon {
    font-size: 40px;
  }
}

@media (max-width: 768px) {
  .performance-container {
    padding: 0;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    padding: 20px 16px;
    text-align: center;
  }

  .title-section {
    flex-direction: column;
    gap: 12px;
  }

  .user-info {
    align-items: center;
  }

  .filter-section,
  .main-content {
    padding: 0 16px;
  }

  .filter-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .filter-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .filter-form :deep(.el-form-item) {
    margin-right: 0;
    margin-bottom: 0;
  }

  .date-picker,
  .product-select {
    width: 100%;
  }

  .overview-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .permission-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .permission-left {
    flex-direction: column;
    text-align: center;
  }

  .data-status {
    align-items: center;
  }

  .status-item {
    align-items: center;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .stat-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .stat-content {
    align-items: center;
  }

  .stat-trend {
    justify-content: center;
  }
}
</style>
