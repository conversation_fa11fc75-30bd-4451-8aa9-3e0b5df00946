<template>
  <div class="hierarchy-tree-view">
    <!-- 工具栏 -->
    <HierarchyToolbar
      :loading="loading"
      @expand-all="handleExpandAll"
      @collapse-all="handleCollapseAll"
      @sort-change="handleSortChange"
      @filter-change="handleFilterChange"
      @view-mode-change="handleViewModeChange"
      @refresh="$emit('refresh')"
    />

    <!-- 层级化业绩数据展示 -->
    <div v-if="filteredHierarchyData && filteredHierarchyData.length > 0" class="tree-container">
      <div 
        v-for="node in filteredHierarchyData" 
        :key="node.nodeId" 
        class="tree-node-wrapper"
        :class="{ 
          'compact-mode': viewMode === 'compact', 
          'card-mode': viewMode === 'card',
          'tree-mode': viewMode === 'tree'
        }"
      >
        <HierarchyNode 
          :node="node"
          :level="0"
          :user-role="userRole"
          :view-mode="viewMode"
          :expanded-nodes="expandedNodes"
          @expand="handleNodeExpand"
          @collapse="handleNodeCollapse"
          @view-detail="handleViewDetail"
        />
      </div>
    </div>

    <!-- 搜索无结果状态 -->
    <div v-else-if="hasSearchFilters" class="no-results-state">
      <div class="empty-content">
        <div class="empty-icon">
          <Icon icon="ep:search" />
        </div>
        <h3 class="empty-title">未找到匹配的结果</h3>
        <p class="empty-desc">请尝试调整搜索条件或筛选器</p>
        <el-button type="primary" @click="clearFilters" class="empty-action">
          <Icon icon="ep:refresh-left" />
          清除筛选条件
        </el-button>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-content">
        <div class="empty-icon">
          <Icon icon="ep:document" />
        </div>
        <h3 class="empty-title">暂无业绩数据</h3>
        <p class="empty-desc">当前时间段内没有业绩数据，请检查筛选条件或联系管理员</p>
        <el-button type="primary" @click="$emit('refresh')" class="empty-action">
          <Icon icon="ep:refresh" />
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { type PerformanceNodeVO } from '@/api/erp/performance'
import HierarchyNode from './HierarchyNode.vue'
import HierarchyToolbar from './HierarchyToolbar.vue'

interface Props {
  hierarchyData: PerformanceNodeVO[]
  userRole: string
  filterParams: any
  loading?: boolean
}

interface Emits {
  (e: 'expand', nodeId: string): void
  (e: 'collapse', nodeId: string): void
  (e: 'view-detail', nodeId: string, nodeType: string): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 状态管理
const expandedNodes = ref<Set<string>>(new Set())
const sortBy = ref('amount')
const sortOrder = ref<'asc' | 'desc'>('desc')
const filters = ref<{ nodeType?: string, keyword?: string }>({})
const viewMode = ref('tree')

/** 是否有搜索筛选条件 */
const hasSearchFilters = computed(() => {
  return !!(filters.value.nodeType || filters.value.keyword)
})

/** 递归排序节点 */
const sortNodes = (nodes: PerformanceNodeVO[]): PerformanceNodeVO[] => {
  const sorted = [...nodes].sort((a, b) => {
    let valueA: any, valueB: any
    
    switch (sortBy.value) {
      case 'amount':
        valueA = a.amount || 0
        valueB = b.amount || 0
        break
      case 'rank':
        valueA = a.rank || 999
        valueB = b.rank || 999
        break
      case 'name':
        valueA = a.nodeName
        valueB = b.nodeName
        break
      case 'memberCount':
        valueA = a.statistics?.totalMemberCount || 0
        valueB = b.statistics?.totalMemberCount || 0
        break
      default:
        return 0
    }
    
    if (sortBy.value === 'name') {
      // 字符串排序
      return sortOrder.value === 'desc' 
        ? valueB.localeCompare(valueA)
        : valueA.localeCompare(valueB)
    } else {
      // 数值排序
      return sortOrder.value === 'desc' 
        ? valueB - valueA 
        : valueA - valueB
    }
  })
  
  // 递归排序子节点
  return sorted.map(node => ({
    ...node,
    children: node.children ? sortNodes(node.children) : undefined
  }))
}

/** 递归过滤节点 */
const filterNodes = (nodes: PerformanceNodeVO[]): PerformanceNodeVO[] => {
  return nodes.filter(node => {
    // 节点类型筛选
    if (filters.value.nodeType && node.nodeType !== filters.value.nodeType) {
      return false
    }
    
    // 关键词搜索
    if (filters.value.keyword) {
      const keyword = filters.value.keyword.toLowerCase()
      const matchesName = node.nodeName.toLowerCase().includes(keyword)
      const matchesChildren = node.children ? 
        filterNodes(node.children).length > 0 : false
      
      if (!matchesName && !matchesChildren) {
        return false
      }
    }
    
    return true
  }).map(node => ({
    ...node,
    children: node.children ? filterNodes(node.children) : undefined
  }))
}

/** 过滤和排序后的数据 */
const filteredHierarchyData = computed(() => {
  if (!props.hierarchyData) return []
  
  let result = [...props.hierarchyData]
  
  // 先过滤
  if (hasSearchFilters.value) {
    result = filterNodes(result)
  }
  
  // 后排序
  result = sortNodes(result)
  
  return result
})

/** 处理节点展开 */
const handleNodeExpand = (nodeId: string) => {
  expandedNodes.value.add(nodeId)
  emit('expand', nodeId)
}

/** 处理节点收起 */
const handleNodeCollapse = (nodeId: string) => {
  expandedNodes.value.delete(nodeId)
  emit('collapse', nodeId)
}

/** 处理查看详情 */
const handleViewDetail = (nodeId: string, nodeType: string) => {
  emit('view-detail', nodeId, nodeType)
}

/** 展开所有节点 */
const handleExpandAll = () => {
  const collectAllNodeIds = (nodes: PerformanceNodeVO[]): string[] => {
    const ids: string[] = []
    nodes.forEach(node => {
      ids.push(node.nodeId)
      if (node.children) {
        ids.push(...collectAllNodeIds(node.children))
      }
    })
    return ids
  }
  
  if (props.hierarchyData) {
    const allNodeIds = collectAllNodeIds(props.hierarchyData)
    expandedNodes.value = new Set(allNodeIds)
  }
}

/** 收起所有节点 */
const handleCollapseAll = () => {
  expandedNodes.value.clear()
}

/** 处理排序变化 */
const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {
  sortBy.value = newSortBy
  sortOrder.value = newSortOrder
}

/** 处理筛选变化 */
const handleFilterChange = (newFilters: { nodeType?: string, keyword?: string }) => {
  filters.value = { ...newFilters }
}

/** 处理显示模式变化 */
const handleViewModeChange = (mode: string) => {
  viewMode.value = mode
}

/** 清除筛选条件 */
const clearFilters = () => {
  filters.value = {}
}

// 初始化默认展开状态
watch(() => props.hierarchyData, (newData) => {
  if (newData) {
    // 自动展开有 defaultExpanded=true 的节点
    const autoExpandNodes = (nodes: PerformanceNodeVO[]) => {
      nodes.forEach(node => {
        if (node.defaultExpanded) {
          expandedNodes.value.add(node.nodeId)
        }
        if (node.children) {
          autoExpandNodes(node.children)
        }
      })
    }
    
    autoExpandNodes(newData)
  }
}, { immediate: true })
</script>

<style scoped>
.hierarchy-tree-view {
  width: 100%;
  position: relative;
}

.tree-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: relative;
}

.tree-node-wrapper {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 树形模式样式 */
.tree-node-wrapper.tree-mode {
  margin-bottom: 4px;
}

/* 紧凑模式样式 */
.tree-node-wrapper.compact-mode {
  margin-bottom: 8px;
}

.tree-node-wrapper.compact-mode :deep(.node-content) {
  padding: 16px 20px;
}

.tree-node-wrapper.compact-mode :deep(.node-name) {
  font-size: 16px;
}

.tree-node-wrapper.compact-mode :deep(.amount-value) {
  font-size: 20px;
}

/* 卡片模式样式 */
.tree-node-wrapper.card-mode {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.6) 100%);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  margin-bottom: 12px;
}

.tree-node-wrapper.card-mode:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

/* 空状态样式 */
.empty-state,
.no-results-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 40px 20px;
}

.empty-content {
  text-align: center;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.empty-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(168, 85, 247, 0.08) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  color: #6366f1;
  margin-bottom: 8px;
}

.empty-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  letter-spacing: -0.01em;
}

.empty-desc {
  margin: 0;
  font-size: 14px;
  color: #64748b;
  line-height: 1.6;
  max-width: 300px;
}

.empty-action {
  margin-top: 8px;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 500;
}

.no-results-state {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(99, 102, 241, 0.1);
  margin: 20px 0;
}

.no-results-state .empty-icon {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.08) 100%);
  color: #f59e0b;
}

/* 加载状态样式 */
.tree-container.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .tree-container {
    gap: 12px;
  }
  
  .tree-node-wrapper.card-mode {
    padding: 8px;
    margin-bottom: 8px;
  }
}

@media (max-width: 768px) {
  .empty-state,
  .no-results-state {
    min-height: 300px;
    padding: 30px 16px;
  }
  
  .empty-icon {
    width: 64px;
    height: 64px;
    font-size: 28px;
  }
  
  .empty-title {
    font-size: 18px;
  }
  
  .empty-desc {
    font-size: 13px;
  }
  
  .tree-container {
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .tree-node-wrapper.compact-mode :deep(.node-content) {
    padding: 12px 16px;
  }
  
  .tree-node-wrapper.card-mode {
    padding: 6px;
    border-radius: 12px;
  }
}
</style>